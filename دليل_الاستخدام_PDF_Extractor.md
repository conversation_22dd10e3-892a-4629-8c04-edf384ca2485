# دليل استخدام مستخرج البيانات من ملفات PDF
# PDF Data Extractor User Guide

## 🎯 نظرة عامة / Overview

هذا البرنامج يقوم باستخراج البيانات التالية من ملفات PDF (حتى لو كانت صور):
- 🏢 اسم الشركة المستفيدة
- 📍 العنوان
- 📞 رقم الهاتف
- 📧 البريد الإلكتروني
- 🏦 اسم البنك المستفيد
- 💳 رقم الحساب
- 🔢 رمز السويفت
- 🏛️ عنوان البنك
- 💰 المبلغ

ويقوم بتصدير البيانات إلى ملف Excel منظم.

---

## 🛠️ التثبيت والإعداد / Installation & Setup

### الخطوة 1: تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث على جهازك.

### الخطوة 2: تثبيت المكتبات المطلوبة
```bash
# الطريقة الأولى: تشغيل سكريبت التثبيت
python install_dependencies.py

# الطريقة الثانية: التثبيت اليدوي
pip install -r requirements.txt

# الطريقة الثالثة: تثبيت كل مكتبة منفصلة
pip install PyPDF2 pdfplumber Pillow pytesseract pdf2image PyMuPDF pandas openpyxl
```

### الخطوة 3: تثبيت Tesseract OCR
لاستخراج النص من الصور، تحتاج لتثبيت Tesseract:

**Windows:**
1. حمل من: https://github.com/UB-Mannheim/tesseract/wiki
2. ثبت البرنامج
3. أضف مسار التثبيت إلى PATH

**macOS:**
```bash
brew install tesseract
brew install tesseract-lang
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-ara  # للعربية
```

---

## 🚀 كيفية الاستخدام / How to Use

### الاستخدام الأساسي:
1. ضع ملفات PDF في نفس مجلد البرنامج
2. شغل البرنامج:
```bash
python pdf_data_extractor.py
```
3. انتظر حتى انتهاء المعالجة
4. ستجد النتائج في ملف `extracted_pdf_data.xlsx`

### الاستخدام المتقدم:
```python
from pdf_data_extractor import PDFDataExtractor

# إنشاء مستخرج البيانات
extractor = PDFDataExtractor()

# معالجة ملف واحد
result = extractor.process_pdf_file("invoice.pdf")

# معالجة مجلد كامل
results = extractor.process_multiple_pdfs("/path/to/pdf/folder")

# تصدير إلى Excel
extractor.export_to_excel("my_results.xlsx")

# إنشاء تقرير ملخص
summary = extractor.generate_summary_report()
```

---

## 📊 مخرجات البرنامج / Program Outputs

### 1. ملف Excel الرئيسي: `extracted_pdf_data.xlsx`
يحتوي على الأعمدة التالية:
- اسم الملف
- اسم الشركة المستفيدة
- العنوان
- رقم الهاتف
- البريد الإلكتروني
- اسم البنك المستفيد
- رقم الحساب
- رمز السويفت
- عنوان البنك
- المبلغ
- طريقة الاستخراج (نص عادي أو OCR)
- تاريخ المعالجة
- حالة المعالجة
- رسالة الخطأ (إن وجدت)

### 2. تقرير الملخص: `extraction_summary.json`
يحتوي على إحصائيات شاملة:
- عدد الملفات المعالجة
- معدل النجاح
- إحصائيات كل حقل
- تاريخ المعالجة

---

## 🔧 الميزات المتقدمة / Advanced Features

### 1. دعم OCR للصور
- يتعامل مع ملفات PDF التي تحتوي على صور
- يدعم النصوص العربية والإنجليزية
- يحسن جودة الصورة تلقائياً

### 2. طرق استخراج متعددة
- PyPDF2: للنصوص العادية
- pdfplumber: للجداول والتخطيطات المعقدة
- PyMuPDF: للملفات المعقدة
- OCR: للصور والنصوص المسحوبة ضوئياً

### 3. أنماط بحث ذكية
- يبحث عن البيانات باستخدام أنماط متعددة
- يدعم النصوص العربية والإنجليزية
- يتعامل مع تنسيقات مختلفة للأرقام والتواريخ

### 4. تنظيف وتحسين البيانات
- ينظف النصوص المستخرجة
- يزيل التكرارات
- يختار أفضل قيمة لكل حقل

---

## 🐛 حل المشاكل الشائعة / Troubleshooting

### مشكلة: "Tesseract not found"
**الحل:**
```python
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

### مشكلة: "No text extracted"
**الأسباب المحتملة:**
- الملف محمي بكلمة مرور
- الملف تالف
- الصورة ذات جودة منخفضة

**الحلول:**
- تأكد من سلامة الملف
- حسن جودة الصورة
- جرب تحويل الملف إلى تنسيق آخر

### مشكلة: "Memory Error"
**الحل:**
- قلل دقة OCR (DPI)
- عالج الملفات واحداً تلو الآخر
- أغلق البرامج الأخرى

### مشكلة: "Arabic text not recognized"
**الحل:**
- تأكد من تثبيت حزمة اللغة العربية لـ Tesseract
- استخدم `lang='ara+eng'` في إعدادات OCR

---

## 📈 تحسين الأداء / Performance Optimization

### 1. لتحسين سرعة المعالجة:
```python
# قلل دقة OCR
images = pdf2image.convert_from_path(pdf_path, dpi=200)  # بدلاً من 300

# استخدم إعدادات OCR أسرع
pytesseract.image_to_string(image, config='--psm 3')  # بدلاً من --psm 6
```

### 2. لتحسين دقة الاستخراج:
```python
# زد دقة OCR
images = pdf2image.convert_from_path(pdf_path, dpi=400)

# استخدم إعدادات OCR أكثر دقة
pytesseract.image_to_string(image, config='--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz@.-+')
```

---

## 🔒 الأمان والخصوصية / Security & Privacy

- البرنامج يعمل محلياً على جهازك
- لا يرسل أي بيانات للإنترنت
- يحفظ النتائج محلياً فقط
- يمكن حذف الملفات المؤقتة بعد المعالجة

---

## 📞 الدعم والمساعدة / Support & Help

### الأخطاء الشائعة:
1. **ImportError**: تأكد من تثبيت جميع المكتبات
2. **FileNotFoundError**: تأكد من وجود ملفات PDF في المجلد
3. **PermissionError**: تأكد من صلاحيات الكتابة في المجلد

### للحصول على مساعدة:
1. راجع رسائل الخطأ في وحدة التحكم
2. تحقق من ملف `extraction_summary.json`
3. جرب معالجة ملف واحد أولاً

---

## 🔄 التحديثات المستقبلية / Future Updates

### المخطط لها:
- [ ] واجهة مستخدم رسومية (GUI)
- [ ] دعم تنسيقات ملفات أخرى (Word, Images)
- [ ] تحسين دقة OCR للنصوص العربية
- [ ] إضافة قوالب مخصصة للاستخراج
- [ ] دعم المعالجة المتوازية للملفات الكبيرة

### التحسينات الحالية:
- ✅ دعم OCR للصور
- ✅ استخراج متعدد الطرق
- ✅ تصدير Excel منسق
- ✅ تقارير مفصلة
- ✅ دعم النصوص العربية والإنجليزية

---

## 📝 مثال على الاستخدام / Usage Example

```python
# مثال شامل
from pdf_data_extractor import PDFDataExtractor
import os

# إنشاء مستخرج البيانات
extractor = PDFDataExtractor()

# معالجة جميع ملفات PDF في المجلد الحالي
print("🔍 البحث عن ملفات PDF...")
results = extractor.process_multiple_pdfs(".")

if results:
    print(f"✅ تم معالجة {len(results)} ملف")
    
    # تصدير النتائج
    if extractor.export_to_excel("البيانات_المستخرجة.xlsx"):
        print("📊 تم التصدير بنجاح!")
    
    # طباعة الملخص
    summary = extractor.generate_summary_report()
    print(f"📈 معدل النجاح: {summary['success_rate']:.1f}%")
else:
    print("❌ لم يتم العثور على ملفات PDF")
```

---

*تم إنشاء هذا الدليل لمساعدتك في الحصول على أفضل النتائج من مستخرج البيانات*
