#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple File Analyzer - Basic version without external dependencies
محلل ملفات بسيط - نسخة أساسية بدون مكتبات خارجية
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET

def analyze_word_document_basic(docx_path):
    """تحليل أساسي لمستند الوورد بدون مكتبات خارجية"""
    try:
        # فتح ملف الوورد كملف ZIP
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            # قراءة المحتوى الرئيسي
            try:
                content = docx_zip.read('word/document.xml')
                root = ET.fromstring(content)
                
                # استخراج النص من XML
                text_content = []
                for elem in root.iter():
                    if elem.text:
                        text_content.append(elem.text.strip())
                
                full_text = ' '.join(text_content)
                
                # تحليل أساسي للحقول المحتملة
                potential_fields = analyze_text_for_fields(full_text)
                
                return {
                    'success': True,
                    'full_text': full_text,
                    'potential_fields': potential_fields,
                    'text_length': len(full_text)
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'error': f'خطأ في قراءة محتوى الوورد: {str(e)}'
                }
                
    except Exception as e:
        return {
            'success': False,
            'error': f'خطأ في فتح ملف الوورد: {str(e)}'
        }

def analyze_text_for_fields(text):
    """تحليل النص للبحث عن حقول محتملة"""
    field_indicators = {
        'name': ['الاسم', 'اسم', 'Name', 'الاسم:', 'اسم:'],
        'company': ['الشركة', 'شركة', 'Company', 'المؤسسة', 'الشركة:', 'شركة:'],
        'address': ['العنوان', 'عنوان', 'Address', 'العنوان:', 'عنوان:'],
        'phone': ['الهاتف', 'هاتف', 'Phone', 'رقم الهاتف', 'الهاتف:', 'هاتف:'],
        'email': ['الإيميل', 'البريد الإلكتروني', 'Email', 'الإيميل:', 'البريد:'],
        'date': ['التاريخ', 'تاريخ', 'Date', 'التاريخ:', 'تاريخ:'],
        'amount': ['المبلغ', 'مبلغ', 'Amount', 'القيمة', 'المبلغ:', 'مبلغ:'],
        'project_type': ['نوع المشروع', 'النوع', 'Type', 'نوع المشروع:', 'النوع:'],
        'capacity': ['القدرة', 'السعة', 'Capacity', 'القدرة:', 'السعة:'],
        'location': ['الموقع', 'موقع', 'Location', 'الموقع:', 'موقع:']
    }
    
    found_fields = {}
    
    for field_type, keywords in field_indicators.items():
        for keyword in keywords:
            if keyword in text:
                # البحث عن النص المحيط بالكلمة المفتاحية
                start_pos = text.find(keyword)
                if start_pos != -1:
                    # أخذ 100 حرف بعد الكلمة المفتاحية
                    context = text[start_pos:start_pos + 100]
                    found_fields[field_type] = {
                        'keyword': keyword,
                        'context': context,
                        'position': start_pos
                    }
                break
    
    return found_fields

def analyze_pdf_basic(pdf_path):
    """تحليل أساسي لملف PDF (محدود بدون مكتبات خارجية)"""
    try:
        # قراءة الملف كبيانات ثنائية
        with open(pdf_path, 'rb') as file:
            content = file.read()
        
        # محاولة استخراج نص بسيط (محدود جداً)
        try:
            # تحويل إلى نص وإزالة الأحرف غير المطبوعة
            text_content = content.decode('latin-1', errors='ignore')
            
            # البحث عن أنماط نصية شائعة
            extracted_info = extract_basic_pdf_info(text_content)
            
            return {
                'success': True,
                'file_size': len(content),
                'extracted_info': extracted_info,
                'raw_text_sample': text_content[:500]  # عينة من النص
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في استخراج النص: {str(e)}',
                'file_size': len(content)
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': f'خطأ في قراءة ملف PDF: {str(e)}'
        }

def extract_basic_pdf_info(text):
    """استخراج معلومات أساسية من نص PDF"""
    info = {}
    
    # البحث عن تواريخ
    import re
    date_patterns = [
        r'2024[.-/]\d{1,2}[.-/]\d{1,2}',
        r'\d{1,2}[.-/]\d{1,2}[.-/]2024',
        r'2024\.\d{1,2}\.\d{1,2}'
    ]
    
    for pattern in date_patterns:
        matches = re.findall(pattern, text)
        if matches:
            info['dates_found'] = matches
            break
    
    # البحث عن أرقام (مبالغ محتملة)
    number_pattern = r'\d{1,3}(?:,\d{3})*(?:\.\d{2})?'
    numbers = re.findall(number_pattern, text)
    if numbers:
        info['numbers_found'] = numbers[:10]  # أول 10 أرقام
    
    # البحث عن كلمات مفتاحية
    keywords = ['FuYue', 'Furniture', 'INVOICE', 'Total', 'Amount', 'Date']
    found_keywords = []
    for keyword in keywords:
        if keyword in text:
            found_keywords.append(keyword)
    
    if found_keywords:
        info['keywords_found'] = found_keywords
    
    return info

def main():
    """الدالة الرئيسية"""
    try:
        print("تحليل الملفات...")
        print("=" * 50)

        pdf_analysis = None
        word_analysis = None

        # تحليل ملف PDF
        pdf_file = "1_FuYue Furniture 花旗银行2024.8.25 INVOICE_已签章 (1).pdf"
        if os.path.exists(pdf_file):
            print(f"تحليل ملف PDF: {pdf_file}")
            try:
                pdf_analysis = analyze_pdf_basic(pdf_file)
                print("نتائج تحليل PDF:")
                print(json.dumps(pdf_analysis, ensure_ascii=False, indent=2))
            except Exception as e:
                print(f"خطأ في تحليل PDF: {e}")
                pdf_analysis = {'success': False, 'error': str(e)}
        else:
            print(f"ملف PDF غير موجود: {pdf_file}")

        print("\n" + "-" * 50 + "\n")

        # تحليل ملف الوورد
        word_file = "طلب طاقة شمسية.docx"
        if os.path.exists(word_file):
            print(f"تحليل ملف الوورد: {word_file}")
            try:
                word_analysis = analyze_word_document_basic(word_file)
                print("نتائج تحليل الوورد:")

                if word_analysis['success']:
                    print(f"طول النص: {word_analysis['text_length']} حرف")
                    print(f"الحقول المكتشفة: {len(word_analysis['potential_fields'])}")

                    if word_analysis['potential_fields']:
                        print("\nالحقول المكتشفة:")
                        for field_type, field_info in word_analysis['potential_fields'].items():
                            print(f"• {field_type}: {field_info['context'][:50]}...")

                    # عرض عينة من النص
                    print(f"\nعينة من النص:")
                    print(word_analysis['full_text'][:300] + "...")
                else:
                    print(f"خطأ: {word_analysis['error']}")
            except Exception as e:
                print(f"خطأ في تحليل الوورد: {e}")
                word_analysis = {'success': False, 'error': str(e)}
        else:
            print(f"ملف الوورد غير موجود: {word_file}")

        # حفظ النتائج
        results = {
            'pdf_analysis': pdf_analysis,
            'word_analysis': word_analysis
        }

        try:
            with open('basic_analysis_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"\nتم حفظ النتائج في: basic_analysis_results.json")
        except Exception as e:
            print(f"خطأ في حفظ النتائج: {e}")

    except Exception as e:
        print(f"خطأ عام في التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
