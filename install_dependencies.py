#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation Script for PDF Data Extractor Dependencies
سكريبت تثبيت المكتبات المطلوبة لمستخرج البيانات من PDF
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل في تثبيت {package}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("تثبيت المكتبات المطلوبة لمستخرج البيانات من PDF")
    print("Installing Dependencies for PDF Data Extractor")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        "PyPDF2==3.0.1",
        "pdfplumber==0.10.3", 
        "Pillow==10.1.0",
        "pytesseract==0.3.10",
        "pdf2image==1.17.0",
        "PyMuPDF==1.23.14",
        "pandas==2.1.4",
        "openpyxl==3.1.2"
    ]
    
    print(f"📦 سيتم تثبيت {len(required_packages)} مكتبة...")
    print()
    
    success_count = 0
    failed_packages = []
    
    for package in required_packages:
        print(f"🔄 تثبيت {package}...")
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    print("=" * 60)
    print("📊 ملخص التثبيت:")
    print(f"✅ نجح: {success_count}/{len(required_packages)}")
    print(f"❌ فشل: {len(failed_packages)}")
    
    if failed_packages:
        print(f"\n❌ المكتبات التي فشل تثبيتها:")
        for package in failed_packages:
            print(f"   - {package}")
        print(f"\n💡 يمكنك محاولة تثبيتها يدوياً باستخدام:")
        print(f"   pip install {' '.join(failed_packages)}")
    
    if success_count == len(required_packages):
        print(f"\n🎉 تم تثبيت جميع المكتبات بنجاح!")
        print(f"✅ يمكنك الآن تشغيل pdf_data_extractor.py")
    else:
        print(f"\n⚠️ بعض المكتبات لم يتم تثبيتها. قد تحتاج لتثبيتها يدوياً.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
