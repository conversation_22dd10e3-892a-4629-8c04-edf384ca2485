<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات الرفع
$upload_dir = 'uploads/';
$output_dir = 'output/';

// إنشاء المجلدات إذا لم تكن موجودة
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}
if (!file_exists($output_dir)) {
    mkdir($output_dir, 0777, true);
}

// التحقق من وجود ملفات مرفوعة
if (!isset($_FILES['pdf_files']) || empty($_FILES['pdf_files']['name'][0])) {
    echo json_encode([
        'success' => false,
        'error' => 'لم يتم رفع أي ملفات'
    ]);
    exit;
}

$uploaded_files = [];
$processed_files = 0;
$extracted_data = [];

// معالجة الملفات المرفوعة
foreach ($_FILES['pdf_files']['name'] as $key => $filename) {
    if ($_FILES['pdf_files']['error'][$key] === UPLOAD_ERR_OK) {
        $temp_file = $_FILES['pdf_files']['tmp_name'][$key];
        $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        // التحقق من نوع الملف
        if ($file_extension !== 'pdf') {
            continue;
        }
        
        // إنشاء اسم ملف آمن
        $safe_filename = time() . '_' . $key . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        $upload_path = $upload_dir . $safe_filename;
        
        // نقل الملف
        if (move_uploaded_file($temp_file, $upload_path)) {
            $uploaded_files[] = $upload_path;
            $processed_files++;
            
            // استخراج البيانات من الملف
            $file_data = extractDataFromPDF($upload_path, $filename);
            $extracted_data[] = $file_data;
        }
    }
}

// إنشاء ملف Excel
$excel_file = createExcelFile($extracted_data);

// تنظيف الملفات المؤقتة
foreach ($uploaded_files as $file) {
    if (file_exists($file)) {
        unlink($file);
    }
}

// إرسال النتيجة
echo json_encode([
    'success' => true,
    'processed_files' => $processed_files,
    'extracted_records' => count($extracted_data),
    'excel_file' => $excel_file,
    'timestamp' => date('Y-m-d H:i:s')
]);

/**
 * استخراج البيانات من ملف PDF
 */
function extractDataFromPDF($file_path, $original_filename) {
    $data = [
        'file_name' => $original_filename,
        'company_name' => '',
        'address' => '',
        'phone' => '',
        'email' => '',
        'bank_name' => '',
        'account_number' => '',
        'swift_code' => '',
        'bank_address' => '',
        'amount' => '',
        'processing_date' => date('Y-m-d H:i:s'),
        'extraction_method' => 'php_basic'
    ];
    
    try {
        // محاولة استخراج النص باستخدام Python إذا كان متوفراً
        $python_result = extractWithPython($file_path);
        if ($python_result) {
            return array_merge($data, $python_result);
        }
        
        // استخراج أساسي من اسم الملف
        $filename_data = extractFromFilename($original_filename);
        $data = array_merge($data, $filename_data);
        
        // محاولة قراءة النص من PDF (محدودة)
        $text_content = extractTextFromPDF($file_path);
        if ($text_content) {
            $text_data = extractFromText($text_content);
            $data = array_merge($data, $text_data);
        }
        
    } catch (Exception $e) {
        $data['error'] = $e->getMessage();
    }
    
    return $data;
}

/**
 * استخراج البيانات باستخدام Python
 */
function extractWithPython($file_path) {
    // التحقق من وجود Python والسكريبت
    $python_script = '../simple_pdf_extractor.py';
    if (!file_exists($python_script)) {
        return false;
    }
    
    // تشغيل السكريبت
    $command = "python \"$python_script\" \"$file_path\" 2>&1";
    $output = shell_exec($command);
    
    // محاولة تحليل النتيجة
    if ($output) {
        // البحث عن JSON في النتيجة
        if (preg_match('/\{.*\}/', $output, $matches)) {
            $json_data = json_decode($matches[0], true);
            if ($json_data) {
                return $json_data;
            }
        }
    }
    
    return false;
}

/**
 * استخراج البيانات من اسم الملف
 */
function extractFromFilename($filename) {
    $data = [];
    
    // استخراج التاريخ
    if (preg_match('/(\d{4}[.-]\d{1,2}[.-]\d{1,2})/', $filename, $matches)) {
        $data['date'] = $matches[1];
    }
    
    // استخراج اسم الشركة (كلمات تبدأ بحرف كبير)
    if (preg_match('/([A-Z][a-zA-Z\s&]+(?:Ltd|Limited|Inc|Corp|Company|Co))/', $filename, $matches)) {
        $data['company_name'] = trim($matches[1]);
    }
    
    // البحث عن أسماء البنوك
    $banks = ['citibank', 'hsbc', 'emirates', 'adcb', 'fab', 'rakbank', 'mashreq', 'cbd', 'nbd'];
    foreach ($banks as $bank) {
        if (stripos($filename, $bank) !== false) {
            $data['bank_name'] = strtoupper($bank);
            break;
        }
    }
    
    return $data;
}

/**
 * استخراج النص من PDF (محدود)
 */
function extractTextFromPDF($file_path) {
    // محاولة قراءة الملف كنص عادي
    $content = file_get_contents($file_path);
    if ($content) {
        // تنظيف النص
        $text = preg_replace('/[^\x20-\x7E\x{0600}-\x{06FF}]/u', ' ', $content);
        return $text;
    }
    
    return '';
}

/**
 * استخراج البيانات من النص
 */
function extractFromText($text) {
    $data = [];
    
    // البحث عن الإيميل
    if (preg_match('/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/', $text, $matches)) {
        $data['email'] = $matches[1];
    }
    
    // البحث عن IBAN
    if (preg_match('/([A-Z]{2}\d{2}[A-Z0-9]{4,30})/', $text, $matches)) {
        $data['account_number'] = $matches[1];
    }
    
    // البحث عن SWIFT
    if (preg_match('/([A-Z]{4}[A-Z]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?)/', $text, $matches)) {
        $data['swift_code'] = $matches[1];
    }
    
    // البحث عن أرقام الهاتف
    if (preg_match('/([+]?[\d\s\-\(\)]{10,})/', $text, $matches)) {
        $data['phone'] = trim($matches[1]);
    }
    
    // البحث عن المبالغ
    if (preg_match('/(\$[\d,]+\.?\d*)/', $text, $matches)) {
        $data['amount'] = $matches[1];
    } elseif (preg_match('/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/', $text, $matches)) {
        $data['amount'] = $matches[1];
    }
    
    return $data;
}

/**
 * إنشاء ملف Excel
 */
function createExcelFile($data) {
    $filename = 'extracted_data_' . date('Y-m-d_H-i-s') . '.csv';
    $filepath = 'output/' . $filename;
    
    $file = fopen($filepath, 'w');
    
    // إضافة BOM للدعم العربي
    fwrite($file, "\xEF\xBB\xBF");
    
    // كتابة العناوين
    $headers = [
        'اسم الملف',
        'اسم الشركة المستفيدة',
        'العنوان',
        'رقم الهاتف',
        'البريد الإلكتروني',
        'اسم البنك المستفيد',
        'رقم الحساب',
        'رمز السويفت',
        'عنوان البنك',
        'المبلغ',
        'تاريخ المعالجة',
        'طريقة الاستخراج'
    ];
    
    fputcsv($file, $headers);
    
    // كتابة البيانات
    foreach ($data as $row) {
        $csv_row = [
            $row['file_name'] ?? '',
            $row['company_name'] ?? '',
            $row['address'] ?? '',
            $row['phone'] ?? '',
            $row['email'] ?? '',
            $row['bank_name'] ?? '',
            $row['account_number'] ?? '',
            $row['swift_code'] ?? '',
            $row['bank_address'] ?? '',
            $row['amount'] ?? '',
            $row['processing_date'] ?? '',
            $row['extraction_method'] ?? ''
        ];
        
        fputcsv($file, $csv_row);
    }
    
    fclose($file);
    
    return $filepath;
}
?>
