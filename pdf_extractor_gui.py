#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Data Extractor with GUI
مستخرج البيانات من PDF مع واجهة رسومية
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import json
from datetime import datetime
import pandas as pd

# استيراد المستخرج البسيط
try:
    from simple_pdf_extractor import SimplePDFExtractor
    EXTRACTOR_AVAILABLE = True
except ImportError:
    EXTRACTOR_AVAILABLE = False

class PDFExtractorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("مستخرج البيانات من ملفات PDF - PDF Data Extractor")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات
        self.selected_files = []
        self.output_folder = tk.StringVar(value=os.getcwd())
        self.is_processing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🔍 مستخرج البيانات من ملفات PDF",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            title_frame,
            text="PDF Data Extractor - استخراج البيانات وتصديرها إلى Excel",
            font=('Arial', 10),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # إطار اختيار الملفات
        files_frame = tk.LabelFrame(
            self.root, 
            text="📁 اختيار ملفات PDF", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        files_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # أزرار اختيار الملفات
        buttons_frame = tk.Frame(files_frame, bg='#f0f0f0')
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        select_files_btn = tk.Button(
            buttons_frame,
            text="📂 اختيار ملفات PDF",
            command=self.select_files,
            bg='#3498db',
            fg='white',
            font=('Arial', 11, 'bold'),
            padx=20,
            pady=8,
            cursor='hand2'
        )
        select_files_btn.pack(side='left', padx=5)
        
        select_folder_btn = tk.Button(
            buttons_frame,
            text="📁 اختيار مجلد",
            command=self.select_folder,
            bg='#9b59b6',
            fg='white',
            font=('Arial', 11, 'bold'),
            padx=20,
            pady=8,
            cursor='hand2'
        )
        select_folder_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ مسح القائمة",
            command=self.clear_files,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 11, 'bold'),
            padx=20,
            pady=8,
            cursor='hand2'
        )
        clear_btn.pack(side='right', padx=5)
        
        # قائمة الملفات المختارة
        self.files_listbox = tk.Listbox(
            files_frame,
            height=8,
            font=('Arial', 10),
            bg='white',
            selectbackground='#3498db'
        )
        self.files_listbox.pack(fill='both', expand=True, padx=10, pady=5)
        
        # شريط التمرير للقائمة
        scrollbar = tk.Scrollbar(self.files_listbox)
        scrollbar.pack(side='right', fill='y')
        self.files_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.files_listbox.yview)
        
        # إطار الإعدادات
        settings_frame = tk.LabelFrame(
            self.root,
            text="⚙️ الإعدادات",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # مجلد الحفظ
        output_frame = tk.Frame(settings_frame, bg='#f0f0f0')
        output_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(
            output_frame,
            text="📂 مجلد الحفظ:",
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(side='left')
        
        output_entry = tk.Entry(
            output_frame,
            textvariable=self.output_folder,
            font=('Arial', 10),
            width=50
        )
        output_entry.pack(side='left', padx=10, fill='x', expand=True)
        
        browse_output_btn = tk.Button(
            output_frame,
            text="تصفح",
            command=self.browse_output_folder,
            bg='#95a5a6',
            fg='white',
            font=('Arial', 9),
            cursor='hand2'
        )
        browse_output_btn.pack(side='right', padx=5)
        
        # إطار المعالجة
        process_frame = tk.Frame(self.root, bg='#f0f0f0')
        process_frame.pack(fill='x', padx=10, pady=10)
        
        # زر البدء
        self.process_btn = tk.Button(
            process_frame,
            text="🚀 بدء استخراج البيانات",
            command=self.start_processing,
            bg='#27ae60',
            fg='white',
            font=('Arial', 14, 'bold'),
            padx=30,
            pady=12,
            cursor='hand2'
        )
        self.process_btn.pack(side='left', padx=10)
        
        # شريط التقدم
        self.progress = ttk.Progressbar(
            process_frame,
            mode='indeterminate',
            length=300
        )
        self.progress.pack(side='left', padx=20, fill='x', expand=True)
        
        # إطار النتائج
        results_frame = tk.LabelFrame(
            self.root,
            text="📊 النتائج والسجلات",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # منطقة النص للنتائج
        self.results_text = scrolledtext.ScrolledText(
            results_frame,
            height=10,
            font=('Consolas', 9),
            bg='#2c3e50',
            fg='#ecf0f1',
            insertbackground='white'
        )
        self.results_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.root,
            text="جاهز للاستخدام - اختر ملفات PDF للبدء",
            relief='sunken',
            anchor='w',
            bg='#34495e',
            fg='white',
            font=('Arial', 9)
        )
        self.status_bar.pack(side='bottom', fill='x')
        
        # رسالة التحقق من المكتبات
        if not EXTRACTOR_AVAILABLE:
            self.log_message("⚠️ تحذير: لم يتم العثور على مكتبات الاستخراج. يرجى تثبيت المكتبات المطلوبة.", "warning")
    
    def log_message(self, message, msg_type="info"):
        """إضافة رسالة إلى سجل النتائج"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if msg_type == "info":
            prefix = "ℹ️"
        elif msg_type == "success":
            prefix = "✅"
        elif msg_type == "warning":
            prefix = "⚠️"
        elif msg_type == "error":
            prefix = "❌"
        else:
            prefix = "📝"
        
        formatted_message = f"[{timestamp}] {prefix} {message}\n"
        
        self.results_text.insert(tk.END, formatted_message)
        self.results_text.see(tk.END)
        self.root.update()
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)
        self.root.update()
    
    def select_files(self):
        """اختيار ملفات PDF"""
        files = filedialog.askopenfilenames(
            title="اختيار ملفات PDF",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if files:
            for file in files:
                if file not in self.selected_files:
                    self.selected_files.append(file)
                    self.files_listbox.insert(tk.END, os.path.basename(file))
            
            self.log_message(f"تم إضافة {len(files)} ملف")
            self.update_status(f"تم اختيار {len(self.selected_files)} ملف")
    
    def select_folder(self):
        """اختيار مجلد يحتوي على ملفات PDF"""
        folder = filedialog.askdirectory(title="اختيار مجلد يحتوي على ملفات PDF")
        
        if folder:
            pdf_files = [f for f in os.listdir(folder) if f.lower().endswith('.pdf')]
            
            if pdf_files:
                for pdf_file in pdf_files:
                    full_path = os.path.join(folder, pdf_file)
                    if full_path not in self.selected_files:
                        self.selected_files.append(full_path)
                        self.files_listbox.insert(tk.END, pdf_file)
                
                self.log_message(f"تم إضافة {len(pdf_files)} ملف من المجلد: {folder}")
                self.update_status(f"تم اختيار {len(self.selected_files)} ملف")
            else:
                messagebox.showwarning("تحذير", "لم يتم العثور على ملفات PDF في المجلد المختار")
    
    def clear_files(self):
        """مسح قائمة الملفات"""
        self.selected_files.clear()
        self.files_listbox.delete(0, tk.END)
        self.log_message("تم مسح قائمة الملفات")
        self.update_status("جاهز للاستخدام - اختر ملفات PDF للبدء")
    
    def browse_output_folder(self):
        """اختيار مجلد الحفظ"""
        folder = filedialog.askdirectory(title="اختيار مجلد الحفظ")
        if folder:
            self.output_folder.set(folder)
            self.log_message(f"تم تغيير مجلد الحفظ إلى: {folder}")
    
    def start_processing(self):
        """بدء معالجة الملفات"""
        if not self.selected_files:
            messagebox.showwarning("تحذير", "يرجى اختيار ملفات PDF أولاً")
            return
        
        if not EXTRACTOR_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات الاستخراج غير متوفرة. يرجى تثبيت المكتبات المطلوبة أولاً.")
            return
        
        if self.is_processing:
            messagebox.showinfo("معلومات", "المعالجة جارية بالفعل...")
            return
        
        # بدء المعالجة في خيط منفصل
        self.is_processing = True
        self.process_btn.config(state='disabled', text="جاري المعالجة...")
        self.progress.start()
        
        processing_thread = threading.Thread(target=self.process_files)
        processing_thread.daemon = True
        processing_thread.start()
    
    def process_files(self):
        """معالجة الملفات (يعمل في خيط منفصل)"""
        try:
            self.log_message("🚀 بدء معالجة الملفات...")
            self.update_status("جاري المعالجة...")
            
            # إنشاء مستخرج البيانات
            extractor = SimplePDFExtractor()
            
            # معالجة كل ملف
            for i, file_path in enumerate(self.selected_files, 1):
                self.log_message(f"📄 معالجة الملف {i}/{len(self.selected_files)}: {os.path.basename(file_path)}")
                
                try:
                    result = extractor.process_pdf_file(file_path)
                    
                    if result.get('success', False):
                        self.log_message(f"✅ نجح استخراج البيانات من: {os.path.basename(file_path)}", "success")
                    else:
                        error_msg = result.get('error', 'خطأ غير محدد')
                        self.log_message(f"❌ فشل في معالجة: {os.path.basename(file_path)} - {error_msg}", "error")
                
                except Exception as e:
                    self.log_message(f"❌ خطأ في معالجة {os.path.basename(file_path)}: {str(e)}", "error")
            
            # تصدير النتائج
            self.log_message("📊 تصدير النتائج إلى Excel...")
            
            output_file = os.path.join(self.output_folder.get(), "extracted_pdf_data.xlsx")
            
            if extractor.export_to_excel(output_file):
                self.log_message(f"✅ تم التصدير بنجاح إلى: {output_file}", "success")
                
                # إنشاء ملخص
                extractor.print_summary()
                
                # حفظ تقرير JSON
                json_file = os.path.join(self.output_folder.get(), "extraction_summary.json")
                summary = {
                    'total_files': len(self.selected_files),
                    'successful': sum(1 for r in extractor.results if r.get('success', False)),
                    'processing_date': datetime.now().isoformat(),
                    'output_excel': output_file
                }
                
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, ensure_ascii=False, indent=2)
                
                self.log_message(f"📋 تم حفظ التقرير في: {json_file}")
                
                # عرض رسالة النجاح
                messagebox.showinfo(
                    "تم بنجاح!", 
                    f"تم استخراج البيانات بنجاح!\n\nملف Excel: {output_file}\nتقرير JSON: {json_file}"
                )
                
            else:
                self.log_message("❌ فشل في تصدير البيانات", "error")
                messagebox.showerror("خطأ", "فشل في تصدير البيانات إلى Excel")
        
        except Exception as e:
            self.log_message(f"❌ خطأ عام في المعالجة: {str(e)}", "error")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء المعالجة:\n{str(e)}")
        
        finally:
            # إنهاء المعالجة
            self.is_processing = False
            self.progress.stop()
            self.process_btn.config(state='normal', text="🚀 بدء استخراج البيانات")
            self.update_status("تم الانتهاء من المعالجة")

def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = PDFExtractorGUI(root)
    
    # رسالة ترحيب
    app.log_message("🎉 مرحباً بك في مستخرج البيانات من ملفات PDF!")
    app.log_message("📋 اختر ملفات PDF واضغط 'بدء استخراج البيانات'")
    
    if not EXTRACTOR_AVAILABLE:
        app.log_message("⚠️ لتثبيت المكتبات المطلوبة، شغل: pip install PyPDF2 pdfplumber pandas openpyxl", "warning")
    
    root.mainloop()

if __name__ == "__main__":
    main()
