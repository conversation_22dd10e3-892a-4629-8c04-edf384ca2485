#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple PDF Data Extractor - Basic Version
مستخرج البيانات البسيط من ملفات PDF - نسخة أساسية
"""

import os
import re
import json
import pandas as pd
from datetime import datetime
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimplePDFExtractor:
    """مستخرج البيانات البسيط من ملفات PDF"""
    
    def __init__(self):
        self.results = []
        
    def extract_basic_info_from_filename(self, filename: str) -> dict:
        """استخراج معلومات أساسية من اسم الملف"""
        info = {
            'file_name': filename,
            'company_name': '',
            'amount': '',
            'date': '',
            'bank_name': ''
        }
        
        # البحث عن أنماط في اسم الملف
        filename_lower = filename.lower()
        
        # استخراج التاريخ
        date_patterns = [
            r'(\d{4}[.-]\d{1,2}[.-]\d{1,2})',
            r'(\d{1,2}[.-]\d{1,2}[.-]\d{4})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                info['date'] = match.group(1)
                break
        
        # البحث عن أسماء البنوك الشائعة
        banks = ['citibank', 'hsbc', 'emirates', 'adcb', 'fab', 'rakbank', 'mashreq', 'cbd', 'nbd']
        for bank in banks:
            if bank in filename_lower:
                info['bank_name'] = bank.upper()
                break
        
        # البحث عن أسماء الشركات (كلمات تحتوي على أحرف كبيرة)
        company_pattern = r'([A-Z][a-zA-Z\s&]+(?:Ltd|Limited|Inc|Corp|Company|Co))'
        company_match = re.search(company_pattern, filename)
        if company_match:
            info['company_name'] = company_match.group(1).strip()
        
        return info
    
    def extract_from_text_basic(self, text: str) -> dict:
        """استخراج أساسي من النص"""
        if not text:
            return {}
        
        extracted = {}
        
        # تنظيف النص
        text = re.sub(r'\s+', ' ', text)
        
        # البحث عن الإيميل
        email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        email_match = re.search(email_pattern, text)
        if email_match:
            extracted['email'] = email_match.group(1)
        
        # البحث عن أرقام الهاتف
        phone_pattern = r'([+]?[\d\s\-\(\)]{10,})'
        phone_matches = re.findall(phone_pattern, text)
        if phone_matches:
            # اختيار أطول رقم (عادة الأكثر اكتمالاً)
            extracted['phone'] = max(phone_matches, key=len).strip()
        
        # البحث عن IBAN
        iban_pattern = r'([A-Z]{2}\d{2}[A-Z0-9]{4,30})'
        iban_match = re.search(iban_pattern, text)
        if iban_match:
            extracted['account_number'] = iban_match.group(1)
        
        # البحث عن SWIFT
        swift_pattern = r'([A-Z]{4}[A-Z]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?)'
        swift_match = re.search(swift_pattern, text)
        if swift_match:
            extracted['swift_code'] = swift_match.group(1)
        
        # البحث عن المبالغ
        amount_patterns = [
            r'(\$[\d,]+\.?\d*)',
            r'(€[\d,]+\.?\d*)',
            r'(£[\d,]+\.?\d*)',
            r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
        ]
        
        for pattern in amount_patterns:
            matches = re.findall(pattern, text)
            if matches:
                # اختيار أكبر مبلغ
                amounts = []
                for match in matches:
                    # تنظيف المبلغ واستخراج الرقم
                    clean_amount = re.sub(r'[^\d.]', '', match)
                    try:
                        amounts.append(float(clean_amount))
                    except:
                        continue
                
                if amounts:
                    max_amount = max(amounts)
                    extracted['amount'] = f"{max_amount:,.2f}"
                break
        
        return extracted
    
    def try_extract_text_methods(self, pdf_path: str) -> str:
        """محاولة استخراج النص بطرق مختلفة"""
        text = ""
        
        # الطريقة الأولى: PyPDF2
        try:
            import PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            if text.strip():
                logger.info("تم استخراج النص باستخدام PyPDF2")
                return text
        except Exception as e:
            logger.warning(f"فشل PyPDF2: {e}")
        
        # الطريقة الثانية: pdfplumber
        try:
            import pdfplumber
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            if text.strip():
                logger.info("تم استخراج النص باستخدام pdfplumber")
                return text
        except Exception as e:
            logger.warning(f"فشل pdfplumber: {e}")
        
        # الطريقة الثالثة: محاولة قراءة كنص عادي
        try:
            with open(pdf_path, 'rb') as file:
                content = file.read()
                text = content.decode('latin-1', errors='ignore')
            if text.strip():
                logger.info("تم استخراج النص كنص عادي")
                return text
        except Exception as e:
            logger.warning(f"فشل القراءة العادية: {e}")
        
        return ""
    
    def process_pdf_file(self, pdf_path: str) -> dict:
        """معالجة ملف PDF واحد"""
        logger.info(f"معالجة: {pdf_path}")
        
        result = {
            'file_name': os.path.basename(pdf_path),
            'file_path': pdf_path,
            'processing_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'success': False,
            'company_name': '',
            'address': '',
            'phone': '',
            'email': '',
            'bank_name': '',
            'account_number': '',
            'swift_code': '',
            'bank_address': '',
            'amount': '',
            'extraction_method': 'filename_only',
            'error': None
        }
        
        try:
            # استخراج من اسم الملف
            filename_info = self.extract_basic_info_from_filename(os.path.basename(pdf_path))
            result.update(filename_info)
            
            # محاولة استخراج النص
            text = self.try_extract_text_methods(pdf_path)
            
            if text.strip():
                # استخراج من النص
                text_info = self.extract_from_text_basic(text)
                result.update(text_info)
                result['extraction_method'] = 'text_extraction'
                result['text_length'] = len(text)
            
            result['success'] = True
            logger.info(f"نجح استخراج البيانات من {pdf_path}")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"خطأ في معالجة {pdf_path}: {e}")
        
        return result
    
    def process_directory(self, directory_path: str = ".") -> list:
        """معالجة جميع ملفات PDF في مجلد"""
        if not os.path.exists(directory_path):
            logger.error(f"المجلد غير موجود: {directory_path}")
            return []
        
        pdf_files = [f for f in os.listdir(directory_path) 
                    if f.lower().endswith('.pdf')]
        
        if not pdf_files:
            logger.warning(f"لم يتم العثور على ملفات PDF في {directory_path}")
            return []
        
        logger.info(f"تم العثور على {len(pdf_files)} ملف PDF")
        
        results = []
        for pdf_file in pdf_files:
            pdf_path = os.path.join(directory_path, pdf_file)
            result = self.process_pdf_file(pdf_path)
            results.append(result)
            self.results.append(result)
        
        return results
    
    def export_to_excel(self, output_path: str = "simple_extracted_data.xlsx") -> bool:
        """تصدير البيانات إلى Excel"""
        try:
            if not self.results:
                logger.warning("لا توجد بيانات للتصدير")
                return False
            
            # إعداد البيانات للتصدير
            export_data = []
            
            for item in self.results:
                row = {
                    'اسم الملف': item.get('file_name', ''),
                    'اسم الشركة المستفيدة': item.get('company_name', ''),
                    'العنوان': item.get('address', ''),
                    'رقم الهاتف': item.get('phone', ''),
                    'البريد الإلكتروني': item.get('email', ''),
                    'اسم البنك المستفيد': item.get('bank_name', ''),
                    'رقم الحساب': item.get('account_number', ''),
                    'رمز السويفت': item.get('swift_code', ''),
                    'عنوان البنك': item.get('bank_address', ''),
                    'المبلغ': item.get('amount', ''),
                    'طريقة الاستخراج': item.get('extraction_method', ''),
                    'تاريخ المعالجة': item.get('processing_date', ''),
                    'حالة المعالجة': 'نجح' if item.get('success') else 'فشل',
                    'رسالة الخطأ': item.get('error', '')
                }
                export_data.append(row)
            
            # إنشاء DataFrame وتصدير
            df = pd.DataFrame(export_data)
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            logger.info(f"تم تصدير {len(export_data)} سجل إلى {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التصدير: {e}")
            return False
    
    def print_summary(self):
        """طباعة ملخص النتائج"""
        if not self.results:
            print("لا توجد نتائج للعرض")
            return
        
        total = len(self.results)
        successful = sum(1 for r in self.results if r.get('success', False))
        
        print("\n" + "="*50)
        print("📊 ملخص النتائج:")
        print("="*50)
        print(f"📁 إجمالي الملفات: {total}")
        print(f"✅ نجح: {successful}")
        print(f"❌ فشل: {total - successful}")
        print(f"📈 معدل النجاح: {(successful/total*100):.1f}%")
        
        # إحصائيات الحقول
        fields = ['company_name', 'phone', 'email', 'bank_name', 'account_number', 'swift_code', 'amount']
        print(f"\n📋 الحقول المملوءة:")
        
        for field in fields:
            filled = sum(1 for r in self.results if r.get(field, '').strip())
            percentage = (filled/successful*100) if successful > 0 else 0
            print(f"   {field}: {filled}/{successful} ({percentage:.1f}%)")

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("مستخرج البيانات البسيط من ملفات PDF")
    print("Simple PDF Data Extractor")
    print("="*60)
    
    # إنشاء المستخرج
    extractor = SimplePDFExtractor()
    
    # معالجة الملفات
    print("🔍 البحث عن ملفات PDF...")
    results = extractor.process_directory(".")
    
    if results:
        # طباعة الملخص
        extractor.print_summary()
        
        # تصدير إلى Excel
        print(f"\n📊 تصدير البيانات...")
        if extractor.export_to_excel():
            print("✅ تم التصدير بنجاح إلى: simple_extracted_data.xlsx")
        else:
            print("❌ فشل في التصدير")
        
        # حفظ النتائج كـ JSON
        try:
            with open('simple_extraction_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print("📄 تم حفظ النتائج التفصيلية في: simple_extraction_results.json")
        except Exception as e:
            print(f"❌ خطأ في حفظ JSON: {e}")
        
    else:
        print("❌ لم يتم العثور على ملفات PDF أو فشل في المعالجة")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()
