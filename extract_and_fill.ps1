# PowerShell Script for Automated Form Filling
# سكريبت PowerShell للملء التلقائي للنماذج

Write-Host "=== نظام الملء التلقائي لفورم طلب الطاقة الشمسية ===" -ForegroundColor Green
Write-Host ""

# متغيرات الملفات
$pdfFile = "1_FuYue Furniture 花旗银行2024.8.25 INVOICE_已签章 (1).pdf"
$wordFile = "طلب طاقة شمسية.docx"
$outputFile = "filled_form_data.json"

# التحقق من وجود الملفات
if (-not (Test-Path $pdfFile)) {
    Write-Host "خطأ: ملف PDF غير موجود - $pdfFile" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $wordFile)) {
    Write-Host "خطأ: ملف الوورد غير موجود - $wordFile" -ForegroundColor Yellow
    exit 1
}

Write-Host "1. تحليل ملف PDF..." -ForegroundColor Cyan

# استخراج معلومات من اسم ملف PDF
$pdfInfo = @{
    "company_name" = "FuYue Furniture"
    "bank_name" = "花旗银行 (Citibank)"
    "document_date" = "2024.8.25"
    "document_type" = "INVOICE"
    "status" = "已签章 (Signed)"
}

Write-Host "   - اسم الشركة: $($pdfInfo.company_name)" -ForegroundColor White
Write-Host "   - البنك: $($pdfInfo.bank_name)" -ForegroundColor White
Write-Host "   - التاريخ: $($pdfInfo.document_date)" -ForegroundColor White
Write-Host "   - نوع المستند: $($pdfInfo.document_type)" -ForegroundColor White

Write-Host ""
Write-Host "2. تحليل فورم الوورد..." -ForegroundColor Cyan

# محاولة استخراج محتوى الوورد
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $zip = [System.IO.Compression.ZipFile]::OpenRead($wordFile)
    $entry = $zip.GetEntry('word/document.xml')
    
    if ($entry) {
        $stream = $entry.Open()
        $reader = New-Object System.IO.StreamReader($stream)
        $xmlContent = $reader.ReadToEnd()
        $reader.Close()
        $stream.Close()
        
        Write-Host "   - تم استخراج محتوى XML من الوورد" -ForegroundColor White
        
        # حفظ محتوى XML للمراجعة
        $xmlContent | Out-File -FilePath "word_document.xml" -Encoding UTF8
        
        # البحث عن حقول محتملة في النص
        $potentialFields = @()
        
        # قائمة الكلمات المفتاحية للحقول
        $fieldKeywords = @{
            "applicant_name" = @("اسم مقدم الطلب", "اسم الطالب", "الاسم", "اسم العميل")
            "company_name" = @("اسم الشركة", "الشركة", "المؤسسة", "اسم المؤسسة")
            "project_location" = @("موقع المشروع", "الموقع", "العنوان", "موقع التركيب")
            "system_capacity" = @("قدرة النظام", "السعة", "القدرة المطلوبة", "قدرة الطاقة")
            "project_type" = @("نوع المشروع", "نوع النظام", "النوع")
            "contact_phone" = @("رقم الهاتف", "الهاتف", "هاتف", "رقم التواصل")
            "email" = @("البريد الإلكتروني", "الإيميل", "البريد")
            "application_date" = @("تاريخ الطلب", "التاريخ", "تاريخ التقديم")
            "estimated_cost" = @("التكلفة المقدرة", "المبلغ", "التكلفة", "القيمة")
        }
        
        foreach ($fieldType in $fieldKeywords.Keys) {
            foreach ($keyword in $fieldKeywords[$fieldType]) {
                if ($xmlContent -match $keyword) {
                    $potentialFields += @{
                        "field_type" = $fieldType
                        "keyword_found" = $keyword
                        "detected" = $true
                    }
                    Write-Host "   - تم العثور على حقل: $fieldType ($keyword)" -ForegroundColor Green
                    break
                }
            }
        }
        
        Write-Host "   - تم تحديد $($potentialFields.Count) حقل محتمل" -ForegroundColor White
    }
    
    $zip.Dispose()
}
catch {
    Write-Host "   - خطأ في قراءة ملف الوورد: $($_.Exception.Message)" -ForegroundColor Red
    $potentialFields = @()
}

Write-Host ""
Write-Host "3. إنشاء بيانات الملء التلقائي..." -ForegroundColor Cyan

# إنشاء بيانات مقترحة للملء
$suggestedData = @{
    "applicant_name" = @{
        "value" = "يرجى إدخال اسم مقدم الطلب"
        "source" = "default"
        "confidence" = 0.3
    }
    "company_name" = @{
        "value" = $pdfInfo.company_name
        "source" = "pdf_filename"
        "confidence" = 0.8
    }
    "project_location" = @{
        "value" = "يرجى تحديد موقع المشروع"
        "source" = "default"
        "confidence" = 0.3
    }
    "system_capacity" = @{
        "value" = "حسب دراسة الموقع"
        "source" = "default"
        "confidence" = 0.3
    }
    "project_type" = @{
        "value" = "نظام طاقة شمسية"
        "source" = "inferred"
        "confidence" = 0.7
    }
    "contact_phone" = @{
        "value" = "يرجى إدخال رقم الهاتف"
        "source" = "default"
        "confidence" = 0.3
    }
    "email" = @{
        "value" = "يرجى إدخال البريد الإلكتروني"
        "source" = "default"
        "confidence" = 0.3
    }
    "application_date" = @{
        "value" = $pdfInfo.document_date
        "source" = "pdf_filename"
        "confidence" = 0.6
    }
    "estimated_cost" = @{
        "value" = "حسب دراسة الموقع والمتطلبات"
        "source" = "default"
        "confidence" = 0.3
    }
}

# عرض البيانات المقترحة
foreach ($field in $suggestedData.Keys) {
    $data = $suggestedData[$field]
    $sourceText = switch ($data.source) {
        "pdf_filename" { "من اسم ملف PDF" }
        "inferred" { "مستنتج" }
        "default" { "افتراضي" }
        default { $data.source }
    }
    Write-Host "   - $field : $($data.value) ($sourceText)" -ForegroundColor White
}

Write-Host ""
Write-Host "4. حفظ النتائج..." -ForegroundColor Cyan

# إنشاء كائن النتائج النهائي
$results = @{
    "timestamp" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    "pdf_analysis" = $pdfInfo
    "word_analysis" = @{
        "fields_detected" = $potentialFields
        "total_fields" = $potentialFields.Count
    }
    "suggested_form_data" = $suggestedData
    "processing_notes" = @(
        "تم استخراج معلومات الشركة والتاريخ من اسم ملف PDF",
        "تم تحليل بنية فورم الوورد للبحث عن الحقول",
        "تم إنشاء بيانات مقترحة للملء التلقائي",
        "يُنصح بمراجعة البيانات قبل الاستخدام النهائي"
    )
}

# تحويل إلى JSON وحفظ
try {
    $jsonOutput = $results | ConvertTo-Json -Depth 10
    $jsonOutput | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "   - تم حفظ النتائج في: $outputFile" -ForegroundColor Green
}
catch {
    Write-Host "   - خطأ في حفظ النتائج: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== تم إكمال العملية ===" -ForegroundColor Green
Write-Host ""
Write-Host "الخطوات التالية المقترحة:" -ForegroundColor Yellow
Write-Host "1. مراجعة ملف النتائج: $outputFile" -ForegroundColor White
Write-Host "2. تحديث البيانات الافتراضية حسب الحاجة" -ForegroundColor White
Write-Host "3. استخدام البيانات لملء النموذج يدوياً أو تلقائياً" -ForegroundColor White
Write-Host "4. التحقق من دقة المعلومات قبل الإرسال" -ForegroundColor White
