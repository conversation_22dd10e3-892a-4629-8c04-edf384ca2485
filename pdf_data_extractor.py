#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Data Extractor with OCR Support
مستخرج البيانات من ملفات PDF مع دعم OCR للصور
"""

import os
import re
import json
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    import PyPDF2
    import pdfplumber
    from PIL import Image
    import pytesseract
    import pdf2image
    import fitz  # PyMuPDF
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"بعض المكتبات غير متوفرة: {e}")
    DEPENDENCIES_AVAILABLE = False

class PDFDataExtractor:
    """مستخرج البيانات من ملفات PDF"""
    
    def __init__(self):
        self.extracted_data = []
        self.field_patterns = self._initialize_patterns()
        
    def _initialize_patterns(self) -> Dict[str, List[str]]:
        """تهيئة أنماط البحث للحقول المختلفة"""
        return {
            'company_name': [
                r'(?:company|شركة|مؤسسة|corporation|corp|ltd|limited|inc)\s*:?\s*([^\n\r]+)',
                r'(?:اسم الشركة|company name|beneficiary|المستفيد)\s*:?\s*([^\n\r]+)',
                r'(?:to|إلى|للسادة)\s*:?\s*([^\n\r]+)',
                r'([A-Z][A-Za-z\s&,.-]+(?:LTD|LIMITED|INC|CORP|COMPANY|CO\.|LLC))',
                r'(شركة\s+[^\n\r]+)',
                r'(مؤسسة\s+[^\n\r]+)'
            ],
            'address': [
                r'(?:address|عنوان|العنوان)\s*:?\s*([^\n\r]+(?:\n[^\n\r]+)*)',
                r'(?:location|الموقع|مكان)\s*:?\s*([^\n\r]+)',
                r'(?:street|شارع|طريق)\s*:?\s*([^\n\r]+)',
                r'(\d+\s+[A-Za-z\s,.-]+(?:street|st|avenue|ave|road|rd))',
                r'([A-Za-z\s,.-]+\d{5}(?:-\d{4})?)',  # ZIP codes
            ],
            'phone': [
                r'(?:phone|tel|telephone|هاتف|رقم الهاتف|تلفون)\s*:?\s*([+]?[\d\s\-\(\)\.]+)',
                r'(?:mobile|موبايل|جوال)\s*:?\s*([+]?[\d\s\-\(\)\.]+)',
                r'([+]?[\d]{1,4}[\s\-]?[\d\s\-\(\)]{7,15})',
                r'(\+\d{1,4}\s?\d{3,4}\s?\d{3,4}\s?\d{3,4})',
                r'(\d{3,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4})'
            ],
            'email': [
                r'(?:email|e-mail|بريد إلكتروني|إيميل)\s*:?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            ],
            'bank_name': [
                r'(?:bank|بنك|مصرف|البنك|المصرف)\s*:?\s*([^\n\r]+)',
                r'(?:banking|مصرفي)\s*:?\s*([^\n\r]+)',
                r'([A-Z][A-Za-z\s&,.-]*BANK[A-Za-z\s&,.-]*)',
                r'(بنك\s+[^\n\r]+)',
                r'(مصرف\s+[^\n\r]+)'
            ],
            'account_number': [
                r'(?:account|حساب|رقم الحساب|account number|acc no|a/c)\s*:?\s*([A-Z0-9\s\-]+)',
                r'(?:iban)\s*:?\s*([A-Z]{2}\d{2}[A-Z0-9]{4,30})',
                r'([A-Z]{2}\d{2}[A-Z0-9]{4,30})',  # IBAN pattern
                r'(\d{10,20})',  # Account numbers
                r'([A-Z0-9]{15,35})'  # Mixed alphanumeric accounts
            ],
            'swift_code': [
                r'(?:swift|سويفت|swift code|bic)\s*:?\s*([A-Z]{4}[A-Z]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?)',
                r'([A-Z]{4}[A-Z]{2}[A-Z0-9]{2}(?:[A-Z0-9]{3})?)',  # SWIFT pattern
                r'(?:bic code|bic)\s*:?\s*([A-Z0-9]{8,11})'
            ],
            'bank_address': [
                r'(?:bank address|عنوان البنك|عنوان المصرف)\s*:?\s*([^\n\r]+(?:\n[^\n\r]+)*)',
                r'(?:branch|فرع|الفرع)\s*:?\s*([^\n\r]+)',
                r'(?:head office|المكتب الرئيسي)\s*:?\s*([^\n\r]+)'
            ],
            'amount': [
                r'(?:amount|مبلغ|المبلغ|total|الإجمالي|قيمة)\s*:?\s*([A-Z]{3}?\s?[\d,]+\.?\d*)',
                r'(\$[\d,]+\.?\d*)',  # Dollar amounts
                r'(€[\d,]+\.?\d*)',   # Euro amounts
                r'(£[\d,]+\.?\d*)',   # Pound amounts
                r'([\d,]+\.?\d*\s?(?:USD|EUR|GBP|AED|SAR|دولار|يورو|جنيه|درهم|ريال))',
                r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # General number pattern
            ]
        }
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """استخراج النص من PDF باستخدام طرق متعددة"""
        text = ""
        
        # الطريقة الأولى: PyPDF2
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            logger.info("تم استخراج النص باستخدام PyPDF2")
        except Exception as e:
            logger.warning(f"فشل PyPDF2: {e}")
        
        # الطريقة الثانية: pdfplumber (أكثر دقة)
        if not text.strip():
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                logger.info("تم استخراج النص باستخدام pdfplumber")
            except Exception as e:
                logger.warning(f"فشل pdfplumber: {e}")
        
        # الطريقة الثالثة: PyMuPDF
        if not text.strip():
            try:
                doc = fitz.open(pdf_path)
                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    page_text = page.get_text()
                    if page_text:
                        text += page_text + "\n"
                doc.close()
                logger.info("تم استخراج النص باستخدام PyMuPDF")
            except Exception as e:
                logger.warning(f"فشل PyMuPDF: {e}")
        
        return text
    
    def extract_text_with_ocr(self, pdf_path: str) -> str:
        """استخراج النص من PDF باستخدام OCR للصور"""
        text = ""
        
        try:
            # تحويل PDF إلى صور
            images = pdf2image.convert_from_path(pdf_path, dpi=300)
            
            for i, image in enumerate(images):
                logger.info(f"معالجة الصفحة {i+1} باستخدام OCR...")
                
                # تحسين جودة الصورة
                image = image.convert('RGB')
                
                # استخراج النص باستخدام Tesseract
                # دعم العربية والإنجليزية
                page_text = pytesseract.image_to_string(
                    image, 
                    lang='ara+eng',
                    config='--psm 6'
                )
                
                if page_text.strip():
                    text += page_text + "\n"
            
            logger.info("تم استخراج النص باستخدام OCR")
            
        except Exception as e:
            logger.error(f"فشل OCR: {e}")
            
        return text
    
    def extract_data_from_text(self, text: str) -> Dict[str, Any]:
        """استخراج البيانات المطلوبة من النص"""
        extracted = {}
        
        # تنظيف النص
        text = re.sub(r'\s+', ' ', text)
        text = text.replace('\n', ' ').replace('\r', ' ')
        
        for field_name, patterns in self.field_patterns.items():
            extracted[field_name] = []
            
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1) if match.groups() else match.group(0)
                    value = value.strip()
                    
                    # تنظيف القيمة
                    value = re.sub(r'\s+', ' ', value)
                    value = value.strip('.,;:')
                    
                    if value and len(value) > 2:  # تجنب القيم القصيرة جداً
                        extracted[field_name].append(value)
        
        # اختيار أفضل قيمة لكل حقل
        final_data = {}
        for field_name, values in extracted.items():
            if values:
                # إزالة التكرارات
                unique_values = list(dict.fromkeys(values))
                # اختيار أطول قيمة (عادة الأكثر اكتمالاً)
                final_data[field_name] = max(unique_values, key=len) if unique_values else ""
            else:
                final_data[field_name] = ""
        
        return final_data
    
    def process_pdf_file(self, pdf_path: str) -> Dict[str, Any]:
        """معالجة ملف PDF واحد"""
        logger.info(f"معالجة الملف: {pdf_path}")
        
        result = {
            'file_name': os.path.basename(pdf_path),
            'file_path': pdf_path,
            'processing_date': datetime.now().isoformat(),
            'extraction_method': '',
            'success': False,
            'error': None
        }
        
        try:
            # محاولة استخراج النص العادي أولاً
            text = self.extract_text_from_pdf(pdf_path)
            
            if text.strip():
                result['extraction_method'] = 'text_extraction'
                logger.info("تم استخراج النص بنجاح")
            else:
                # إذا فشل، استخدم OCR
                logger.info("النص غير متوفر، استخدام OCR...")
                text = self.extract_text_with_ocr(pdf_path)
                result['extraction_method'] = 'ocr'
            
            if text.strip():
                # استخراج البيانات
                extracted_data = self.extract_data_from_text(text)
                result.update(extracted_data)
                result['success'] = True
                result['extracted_text_length'] = len(text)
                
                logger.info(f"تم استخراج البيانات بنجاح من {pdf_path}")
            else:
                result['error'] = "لم يتم العثور على نص في الملف"
                logger.warning(f"لم يتم العثور على نص في {pdf_path}")
                
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"خطأ في معالجة {pdf_path}: {e}")
        
        return result
    
    def process_multiple_pdfs(self, pdf_directory: str) -> List[Dict[str, Any]]:
        """معالجة عدة ملفات PDF في مجلد"""
        results = []
        
        if not os.path.exists(pdf_directory):
            logger.error(f"المجلد غير موجود: {pdf_directory}")
            return results
        
        pdf_files = [f for f in os.listdir(pdf_directory) if f.lower().endswith('.pdf')]
        
        if not pdf_files:
            logger.warning(f"لم يتم العثور على ملفات PDF في {pdf_directory}")
            return results
        
        logger.info(f"تم العثور على {len(pdf_files)} ملف PDF")
        
        for pdf_file in pdf_files:
            pdf_path = os.path.join(pdf_directory, pdf_file)
            result = self.process_pdf_file(pdf_path)
            results.append(result)
            self.extracted_data.append(result)
        
        return results
    
    def export_to_excel(self, output_path: str = "extracted_data.xlsx") -> bool:
        """تصدير البيانات إلى Excel"""
        try:
            if not self.extracted_data:
                logger.warning("لا توجد بيانات للتصدير")
                return False
            
            # إعداد البيانات للتصدير
            export_data = []
            
            for item in self.extracted_data:
                if item.get('success', False):
                    row = {
                        'اسم الملف': item.get('file_name', ''),
                        'اسم الشركة المستفيدة': item.get('company_name', ''),
                        'العنوان': item.get('address', ''),
                        'رقم الهاتف': item.get('phone', ''),
                        'البريد الإلكتروني': item.get('email', ''),
                        'اسم البنك المستفيد': item.get('bank_name', ''),
                        'رقم الحساب': item.get('account_number', ''),
                        'رمز السويفت': item.get('swift_code', ''),
                        'عنوان البنك': item.get('bank_address', ''),
                        'المبلغ': item.get('amount', ''),
                        'طريقة الاستخراج': item.get('extraction_method', ''),
                        'تاريخ المعالجة': item.get('processing_date', ''),
                        'حالة المعالجة': 'نجح' if item.get('success') else 'فشل',
                        'رسالة الخطأ': item.get('error', '')
                    }
                    export_data.append(row)
            
            # إنشاء DataFrame
            df = pd.DataFrame(export_data)
            
            # تصدير إلى Excel مع تنسيق
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='البيانات المستخرجة', index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets['البيانات المستخرجة']
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"تم تصدير البيانات إلى: {output_path}")
            logger.info(f"عدد السجلات المصدرة: {len(export_data)}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التصدير: {e}")
            return False
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """إنشاء تقرير ملخص"""
        total_files = len(self.extracted_data)
        successful_files = sum(1 for item in self.extracted_data if item.get('success', False))
        failed_files = total_files - successful_files
        
        # إحصائيات الحقول
        field_stats = {}
        for field in ['company_name', 'address', 'phone', 'email', 'bank_name', 
                     'account_number', 'swift_code', 'bank_address', 'amount']:
            filled_count = sum(1 for item in self.extracted_data 
                             if item.get('success', False) and item.get(field, '').strip())
            field_stats[field] = {
                'filled': filled_count,
                'percentage': (filled_count / successful_files * 100) if successful_files > 0 else 0
            }
        
        return {
            'total_files_processed': total_files,
            'successful_extractions': successful_files,
            'failed_extractions': failed_files,
            'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0,
            'field_statistics': field_stats,
            'processing_date': datetime.now().isoformat()
        }

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("مستخرج البيانات من ملفات PDF مع دعم OCR")
    print("PDF Data Extractor with OCR Support")
    print("=" * 70)
    
    if not DEPENDENCIES_AVAILABLE:
        print("❌ بعض المكتبات المطلوبة غير متوفرة!")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install PyPDF2 pdfplumber pillow pytesseract pdf2image PyMuPDF pandas openpyxl")
        return
    
    # إنشاء مستخرج البيانات
    extractor = PDFDataExtractor()
    
    # معالجة الملفات في المجلد الحالي
    current_directory = "."
    
    print(f"🔍 البحث عن ملفات PDF في: {current_directory}")
    results = extractor.process_multiple_pdfs(current_directory)
    
    if results:
        print(f"✅ تم معالجة {len(results)} ملف")
        
        # تصدير إلى Excel
        print("📊 تصدير البيانات إلى Excel...")
        if extractor.export_to_excel("extracted_pdf_data.xlsx"):
            print("✅ تم التصدير بنجاح!")
        else:
            print("❌ فشل في التصدير")
        
        # إنشاء تقرير ملخص
        summary = extractor.generate_summary_report()
        
        print("\n" + "=" * 50)
        print("📈 تقرير الملخص:")
        print("=" * 50)
        print(f"📁 إجمالي الملفات: {summary['total_files_processed']}")
        print(f"✅ نجح: {summary['successful_extractions']}")
        print(f"❌ فشل: {summary['failed_extractions']}")
        print(f"📊 معدل النجاح: {summary['success_rate']:.1f}%")
        
        print(f"\n📋 إحصائيات الحقول:")
        for field, stats in summary['field_statistics'].items():
            print(f"   {field}: {stats['filled']}/{summary['successful_extractions']} ({stats['percentage']:.1f}%)")
        
        # حفظ التقرير
        with open('extraction_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 الملفات المُنشأة:")
        print(f"   📊 extracted_pdf_data.xlsx - البيانات المستخرجة")
        print(f"   📋 extraction_summary.json - تقرير الملخص")
        
    else:
        print("❌ لم يتم العثور على ملفات PDF أو فشل في المعالجة")

if __name__ == "__main__":
    main()
