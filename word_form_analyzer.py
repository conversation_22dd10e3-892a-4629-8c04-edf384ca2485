#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word Form Analyzer for Solar Energy Application
تحليل فورم الوورد لطلب الطاقة الشمسية
"""

from docx import Document
from docx.shared import Inches
import json
from typing import Dict, List, Any

class WordFormAnalyzer:
    def __init__(self, docx_path: str):
        self.docx_path = docx_path
        self.document = None
        self.form_fields = {}
        self.tables_data = []
        self.paragraphs_data = []
    
    def load_document(self):
        """تحميل مستند الوورد"""
        try:
            self.document = Document(self.docx_path)
            print(f"تم تحميل المستند: {self.docx_path}")
            return True
        except Exception as e:
            print(f"خطأ في تحميل المستند: {e}")
            return False
    
    def analyze_paragraphs(self):
        """تحليل الفقرات في المستند"""
        print("تحليل الفقرات...")
        
        for i, paragraph in enumerate(self.document.paragraphs):
            if paragraph.text.strip():
                para_data = {
                    'index': i,
                    'text': paragraph.text.strip(),
                    'style': paragraph.style.name if paragraph.style else 'Normal'
                }
                self.paragraphs_data.append(para_data)
                
                # البحث عن حقول محتملة (نص يحتوي على نقاط أو خطوط فارغة)
                if any(char in paragraph.text for char in ['_', ':', '......', '___']):
                    self.identify_potential_field(paragraph.text, i)
    
    def analyze_tables(self):
        """تحليل الجداول في المستند"""
        print("تحليل الجداول...")
        
        for table_idx, table in enumerate(self.document.tables):
            table_data = {
                'table_index': table_idx,
                'rows': len(table.rows),
                'cols': len(table.columns),
                'data': []
            }
            
            for row_idx, row in enumerate(table.rows):
                row_data = []
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    row_data.append({
                        'cell_index': cell_idx,
                        'text': cell_text,
                        'is_potential_field': self.is_potential_field(cell_text)
                    })
                table_data['data'].append(row_data)
            
            self.tables_data.append(table_data)
    
    def identify_potential_field(self, text: str, paragraph_index: int):
        """تحديد الحقول المحتملة من النص"""
        # أنماط شائعة للحقول في النماذج العربية
        field_patterns = {
            'name': ['الاسم', 'اسم', 'Name'],
            'company': ['الشركة', 'شركة', 'Company', 'المؤسسة'],
            'address': ['العنوان', 'عنوان', 'Address'],
            'phone': ['الهاتف', 'هاتف', 'Phone', 'رقم الهاتف'],
            'email': ['الإيميل', 'البريد الإلكتروني', 'Email'],
            'date': ['التاريخ', 'تاريخ', 'Date'],
            'amount': ['المبلغ', 'مبلغ', 'Amount', 'القيمة'],
            'project_type': ['نوع المشروع', 'النوع', 'Type'],
            'capacity': ['القدرة', 'السعة', 'Capacity'],
            'location': ['الموقع', 'موقع', 'Location']
        }
        
        for field_type, keywords in field_patterns.items():
            for keyword in keywords:
                if keyword in text:
                    self.form_fields[field_type] = {
                        'paragraph_index': paragraph_index,
                        'original_text': text,
                        'field_type': field_type,
                        'keyword_found': keyword
                    }
                    break
    
    def is_potential_field(self, text: str) -> bool:
        """تحديد ما إذا كان النص يحتوي على حقل محتمل"""
        indicators = ['_', ':', '......', '___', '...', '____']
        return any(indicator in text for indicator in indicators)
    
    def extract_form_structure(self) -> Dict[str, Any]:
        """استخراج بنية النموذج"""
        if not self.document:
            if not self.load_document():
                return {}
        
        self.analyze_paragraphs()
        self.analyze_tables()
        
        structure = {
            'total_paragraphs': len(self.document.paragraphs),
            'total_tables': len(self.document.tables),
            'identified_fields': self.form_fields,
            'paragraphs': self.paragraphs_data,
            'tables': self.tables_data
        }
        
        return structure
    
    def print_analysis_summary(self):
        """طباعة ملخص التحليل"""
        print("\n" + "="*60)
        print("ملخص تحليل فورم الوورد")
        print("="*60)
        
        print(f"عدد الفقرات: {len(self.paragraphs_data)}")
        print(f"عدد الجداول: {len(self.tables_data)}")
        print(f"الحقول المحددة: {len(self.form_fields)}")
        
        if self.form_fields:
            print("\nالحقول المكتشفة:")
            print("-" * 30)
            for field_type, field_info in self.form_fields.items():
                print(f"• {field_type}: {field_info['original_text'][:50]}...")
        
        print("\nعينة من الفقرات:")
        print("-" * 30)
        for para in self.paragraphs_data[:5]:  # أول 5 فقرات
            print(f"[{para['index']}] {para['text'][:80]}...")
        
        if self.tables_data:
            print(f"\nالجداول الموجودة: {len(self.tables_data)}")
            for i, table in enumerate(self.tables_data):
                print(f"جدول {i+1}: {table['rows']} صف × {table['cols']} عمود")

def main():
    """الدالة الرئيسية لاختبار تحليل فورم الوورد"""
    word_file = "طلب طاقة شمسية.docx"
    
    analyzer = WordFormAnalyzer(word_file)
    structure = analyzer.extract_form_structure()
    
    if structure:
        analyzer.print_analysis_summary()
        
        # حفظ البنية في ملف JSON
        with open('word_form_structure.json', 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"\nتم حفظ بنية النموذج في: word_form_structure.json")
    else:
        print("فشل في تحليل فورم الوورد")

if __name__ == "__main__":
    main()
