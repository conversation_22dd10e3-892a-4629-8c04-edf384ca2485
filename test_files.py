import os

print("Testing file access...")
print("Current directory:", os.getcwd())
print("Files in directory:")
for file in os.listdir('.'):
    print(f"  - {file}")
    print(f"    Exists: {os.path.exists(file)}")
    print(f"    Size: {os.path.getsize(file) if os.path.exists(file) else 'N/A'}")

# Test Word file
word_file = "طلب طاقة شمسية.docx"
if os.path.exists(word_file):
    print(f"\nWord file found: {word_file}")
    try:
        import zipfile
        with zipfile.ZipFile(word_file, 'r') as z:
            print("ZIP contents:", z.namelist()[:5])
    except Exception as e:
        print(f"Error reading as ZIP: {e}")

# Test PDF file
pdf_file = "1_FuYue Furniture 花旗银行2024.8.25 INVOICE_已签章 (1).pdf"
if os.path.exists(pdf_file):
    print(f"\nPDF file found: {pdf_file}")
    with open(pdf_file, 'rb') as f:
        first_bytes = f.read(100)
        print(f"First 100 bytes: {first_bytes[:50]}...")

print("\nTest completed!")
