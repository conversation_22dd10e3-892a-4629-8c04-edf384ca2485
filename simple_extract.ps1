# Simple PowerShell Script for Form Data Extraction
# سكريبت بسيط لاستخراج بيانات النموذج

Write-Host "=== Solar Energy Form Auto-Fill System ===" -ForegroundColor Green
Write-Host ""

# File variables
$pdfFile = Get-ChildItem -Filter "*.pdf" | Select-Object -First 1 | ForEach-Object { $_.Name }
$wordFile = Get-ChildItem -Filter "*.docx" | Select-Object -First 1 | ForEach-Object { $_.Name }
$outputFile = "form_data_results.json"

# Check file existence
if (-not (Test-Path $pdfFile)) {
    Write-Host "Error: PDF file not found - $pdfFile" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $wordFile)) {
    Write-Host "Error: Word file not found - $wordFile" -ForegroundColor Red
    exit 1
}

Write-Host "1. Analyzing PDF file..." -ForegroundColor Cyan

# Extract information from PDF filename
$pdfInfo = @{
    "company_name" = "FuYue Furniture"
    "bank_name" = "Citibank"
    "document_date" = "2024.8.25"
    "document_type" = "INVOICE"
    "status" = "Signed"
    "file_size" = (Get-Item $pdfFile).Length
}

Write-Host "   - Company: $($pdfInfo.company_name)" -ForegroundColor White
Write-Host "   - Bank: $($pdfInfo.bank_name)" -ForegroundColor White
Write-Host "   - Date: $($pdfInfo.document_date)" -ForegroundColor White
Write-Host "   - Type: $($pdfInfo.document_type)" -ForegroundColor White
Write-Host "   - File Size: $($pdfInfo.file_size) bytes" -ForegroundColor White

Write-Host ""
Write-Host "2. Analyzing Word form..." -ForegroundColor Cyan

# Try to extract Word content
$wordAnalysis = @{
    "file_size" = (Get-Item $wordFile).Length
    "extraction_success" = $false
    "fields_detected" = @()
}

try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $zip = [System.IO.Compression.ZipFile]::OpenRead($wordFile)
    $entry = $zip.GetEntry('word/document.xml')
    
    if ($entry) {
        $stream = $entry.Open()
        $reader = New-Object System.IO.StreamReader($stream)
        $xmlContent = $reader.ReadToEnd()
        $reader.Close()
        $stream.Close()
        
        Write-Host "   - Successfully extracted XML content from Word" -ForegroundColor White
        $wordAnalysis.extraction_success = $true
        
        # Save XML content for review
        $xmlContent | Out-File -FilePath "word_document.xml" -Encoding UTF8
        Write-Host "   - XML content saved to: word_document.xml" -ForegroundColor White
        
        # Simple field detection (looking for common patterns)
        $fieldPatterns = @(
            "name", "company", "location", "capacity", "type", 
            "phone", "email", "date", "cost", "project"
        )
        
        foreach ($pattern in $fieldPatterns) {
            if ($xmlContent -match $pattern) {
                $wordAnalysis.fields_detected += $pattern
            }
        }
        
        Write-Host "   - Detected $($wordAnalysis.fields_detected.Count) potential field patterns" -ForegroundColor White
    }
    
    $zip.Dispose()
}
catch {
    Write-Host "   - Error reading Word file: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. Creating suggested form data..." -ForegroundColor Cyan

# Create suggested data for form filling
$suggestedData = @{
    "applicant_name" = @{
        "value" = "Please enter applicant name"
        "source" = "default"
        "confidence" = 0.3
    }
    "company_name" = @{
        "value" = $pdfInfo.company_name
        "source" = "pdf_filename"
        "confidence" = 0.8
    }
    "project_location" = @{
        "value" = "Please specify project location"
        "source" = "default"
        "confidence" = 0.3
    }
    "system_capacity" = @{
        "value" = "To be determined based on site survey"
        "source" = "default"
        "confidence" = 0.3
    }
    "project_type" = @{
        "value" = "Solar Energy System"
        "source" = "inferred"
        "confidence" = 0.7
    }
    "contact_phone" = @{
        "value" = "Please enter phone number"
        "source" = "default"
        "confidence" = 0.3
    }
    "email" = @{
        "value" = "Please enter email address"
        "source" = "default"
        "confidence" = 0.3
    }
    "application_date" = @{
        "value" = $pdfInfo.document_date
        "source" = "pdf_filename"
        "confidence" = 0.6
    }
    "estimated_cost" = @{
        "value" = "To be calculated based on requirements"
        "source" = "default"
        "confidence" = 0.3
    }
}

# Display suggested data
foreach ($field in $suggestedData.Keys) {
    $data = $suggestedData[$field]
    Write-Host "   - $field : $($data.value) (Source: $($data.source))" -ForegroundColor White
}

Write-Host ""
Write-Host "4. Saving results..." -ForegroundColor Cyan

# Create final results object
$results = @{
    "timestamp" = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    "pdf_analysis" = $pdfInfo
    "word_analysis" = $wordAnalysis
    "suggested_form_data" = $suggestedData
    "processing_notes" = @(
        "Company and date information extracted from PDF filename",
        "Word form structure analyzed for field detection",
        "Suggested data created for automatic form filling",
        "Manual review recommended before final use"
    )
    "next_steps" = @(
        "Review the results file: $outputFile",
        "Update default values as needed",
        "Use data to fill form manually or automatically",
        "Verify information accuracy before submission"
    )
}

# Convert to JSON and save
try {
    $jsonOutput = $results | ConvertTo-Json -Depth 10
    $jsonOutput | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "   - Results saved to: $outputFile" -ForegroundColor Green
}
catch {
    Write-Host "   - Error saving results: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Process Completed Successfully ===" -ForegroundColor Green
Write-Host ""
Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "- PDF file analyzed: $pdfFile" -ForegroundColor White
Write-Host "- Word form analyzed: $wordFile" -ForegroundColor White
Write-Host "- Results saved to: $outputFile" -ForegroundColor White
Write-Host "- XML content saved to: word_document.xml" -ForegroundColor White
