<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستخرج البيانات من ملفات PDF</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 800px;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .progress-container {
            display: none;
            margin: 20px 0;
        }
        .results-container {
            display: none;
            margin: 20px 0;
        }
        .btn-custom {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            color: white;
        }
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-file-pdf"></i> مستخرج البيانات من ملفات PDF</h1>
                <p class="mb-0">ارفع ملفات PDF واحصل على البيانات في ملف Excel</p>
            </div>

            <!-- Main Content -->
            <div class="p-4">
                <!-- Upload Form -->
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h4>اسحب وأفلت ملفات PDF هنا</h4>
                        <p class="text-muted">أو اضغط لاختيار الملفات</p>
                        <input type="file" id="pdfFiles" name="pdf_files[]" multiple accept=".pdf" style="display: none;">
                        <button type="button" class="btn btn-custom" onclick="document.getElementById('pdfFiles').click();">
                            <i class="fas fa-folder-open"></i> اختيار ملفات PDF
                        </button>
                    </div>

                    <!-- Selected Files List -->
                    <div id="filesList" class="file-list" style="display: none;"></div>

                    <!-- Process Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-custom btn-lg" id="processBtn">
                            <i class="fas fa-cogs"></i> بدء استخراج البيانات
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="clearFiles()">
                            <i class="fas fa-trash"></i> مسح الكل
                        </button>
                    </div>
                </form>

                <!-- Progress -->
                <div class="progress-container" id="progressContainer">
                    <h5><i class="fas fa-spinner fa-spin"></i> جاري المعالجة...</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div id="statusMessages"></div>
                </div>

                <!-- Results -->
                <div class="results-container" id="resultsContainer">
                    <h5><i class="fas fa-check-circle text-success"></i> النتائج</h5>
                    <div id="resultsContent"></div>
                </div>

                <!-- Instructions -->
                <div class="mt-5">
                    <h5><i class="fas fa-info-circle"></i> البيانات المستخرجة:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-building text-primary"></i> اسم الشركة المستفيدة</li>
                                <li><i class="fas fa-map-marker-alt text-primary"></i> العنوان</li>
                                <li><i class="fas fa-phone text-primary"></i> رقم الهاتف</li>
                                <li><i class="fas fa-envelope text-primary"></i> البريد الإلكتروني</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-university text-primary"></i> اسم البنك المستفيد</li>
                                <li><i class="fas fa-credit-card text-primary"></i> رقم الحساب</li>
                                <li><i class="fas fa-code text-primary"></i> رمز السويفت</li>
                                <li><i class="fas fa-dollar-sign text-primary"></i> المبلغ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedFiles = [];

        // File input change event
        document.getElementById('pdfFiles').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // Drag and drop events
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        function handleFiles(files) {
            for (let file of files) {
                if (file.type === 'application/pdf') {
                    selectedFiles.push(file);
                }
            }
            updateFilesList();
        }

        function updateFilesList() {
            const filesList = document.getElementById('filesList');
            if (selectedFiles.length === 0) {
                filesList.style.display = 'none';
                return;
            }

            filesList.style.display = 'block';
            filesList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span><i class="fas fa-file-pdf text-danger"></i> ${file.name}</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                filesList.appendChild(fileItem);
            });
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFilesList();
        }

        function clearFiles() {
            selectedFiles = [];
            document.getElementById('pdfFiles').value = '';
            updateFilesList();
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (selectedFiles.length === 0) {
                alert('يرجى اختيار ملفات PDF أولاً');
                return;
            }

            processFiles();
        });

        function processFiles() {
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('pdf_files[]', file);
            });

            // Show progress
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('processBtn').disabled = true;

            // Simulate progress
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const statusMessages = document.getElementById('statusMessages');
            
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 500);

            // Send request
            fetch('process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                    document.getElementById('resultsContainer').style.display = 'block';
                    document.getElementById('processBtn').disabled = false;
                    
                    displayResults(data);
                }, 1000);
            })
            .catch(error => {
                clearInterval(progressInterval);
                document.getElementById('progressContainer').style.display = 'none';
                document.getElementById('processBtn').disabled = false;
                
                statusMessages.innerHTML = `
                    <div class="status-message status-error">
                        <i class="fas fa-exclamation-triangle"></i> حدث خطأ: ${error.message}
                    </div>
                `;
            });
        }

        function displayResults(data) {
            const resultsContent = document.getElementById('resultsContent');
            
            if (data.success) {
                resultsContent.innerHTML = `
                    <div class="status-message status-success">
                        <i class="fas fa-check-circle"></i> تم استخراج البيانات بنجاح!
                    </div>
                    <div class="mt-3">
                        <h6>الملفات المعالجة: ${data.processed_files}</h6>
                        <h6>البيانات المستخرجة: ${data.extracted_records}</h6>
                        <a href="${data.excel_file}" class="btn btn-success mt-2" download>
                            <i class="fas fa-download"></i> تحميل ملف Excel
                        </a>
                    </div>
                `;
            } else {
                resultsContent.innerHTML = `
                    <div class="status-message status-error">
                        <i class="fas fa-exclamation-triangle"></i> فشل في المعالجة: ${data.error}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
