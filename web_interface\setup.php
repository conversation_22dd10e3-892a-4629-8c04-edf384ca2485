<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد مستخرج البيانات من PDF</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .check-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .check-success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .check-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .check-error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> إعداد مستخرج البيانات من PDF</h1>
            <p class="mb-0">فحص النظام والمتطلبات</p>
        </div>

        <div class="p-4">
            <h3><i class="fas fa-check-circle"></i> فحص المتطلبات</h3>
            
            <?php
            $checks = [];
            
            // فحص PHP
            $php_version = phpversion();
            $checks[] = [
                'name' => 'إصدار PHP',
                'status' => version_compare($php_version, '7.4', '>=') ? 'success' : 'error',
                'message' => "PHP $php_version " . (version_compare($php_version, '7.4', '>=') ? '✅' : '❌ يتطلب PHP 7.4 أو أحدث'),
                'required' => true
            ];
            
            // فحص المجلدات
            $upload_dir = 'uploads/';
            $output_dir = 'output/';
            
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            if (!file_exists($output_dir)) {
                mkdir($output_dir, 0777, true);
            }
            
            $checks[] = [
                'name' => 'مجلد الرفع',
                'status' => is_writable($upload_dir) ? 'success' : 'error',
                'message' => is_writable($upload_dir) ? "✅ $upload_dir قابل للكتابة" : "❌ $upload_dir غير قابل للكتابة",
                'required' => true
            ];
            
            $checks[] = [
                'name' => 'مجلد النتائج',
                'status' => is_writable($output_dir) ? 'success' : 'error',
                'message' => is_writable($output_dir) ? "✅ $output_dir قابل للكتابة" : "❌ $output_dir غير قابل للكتابة",
                'required' => true
            ];
            
            // فحص رفع الملفات
            $upload_enabled = ini_get('file_uploads');
            $checks[] = [
                'name' => 'رفع الملفات',
                'status' => $upload_enabled ? 'success' : 'error',
                'message' => $upload_enabled ? '✅ رفع الملفات مفعل' : '❌ رفع الملفات معطل',
                'required' => true
            ];
            
            // فحص حجم الرفع
            $max_upload = ini_get('upload_max_filesize');
            $max_post = ini_get('post_max_size');
            $checks[] = [
                'name' => 'حجم الرفع الأقصى',
                'status' => 'success',
                'message' => "✅ حد الرفع: $max_upload، حد POST: $max_post",
                'required' => false
            ];
            
            // فحص Python
            $python_available = false;
            $python_output = shell_exec('python --version 2>&1');
            if ($python_output && strpos($python_output, 'Python') !== false) {
                $python_available = true;
            } else {
                $python_output = shell_exec('python3 --version 2>&1');
                if ($python_output && strpos($python_output, 'Python') !== false) {
                    $python_available = true;
                }
            }
            
            $checks[] = [
                'name' => 'Python (اختياري)',
                'status' => $python_available ? 'success' : 'warning',
                'message' => $python_available ? "✅ Python متوفر: " . trim($python_output) : '⚠️ Python غير متوفر (سيتم الاستخراج الأساسي فقط)',
                'required' => false
            ];
            
            // فحص سكريبت Python
            $python_script = '../simple_pdf_extractor.py';
            $script_exists = file_exists($python_script);
            $checks[] = [
                'name' => 'سكريبت Python',
                'status' => $script_exists ? 'success' : 'warning',
                'message' => $script_exists ? '✅ سكريبت الاستخراج موجود' : '⚠️ سكريبت الاستخراج غير موجود',
                'required' => false
            ];
            
            // عرض النتائج
            $all_required_ok = true;
            foreach ($checks as $check) {
                $class = 'check-' . $check['status'];
                if ($check['required'] && $check['status'] === 'error') {
                    $all_required_ok = false;
                }
                
                echo "<div class='check-item $class'>";
                echo "<strong>{$check['name']}</strong><br>";
                echo $check['message'];
                if ($check['required']) {
                    echo " <span class='badge bg-danger'>مطلوب</span>";
                }
                echo "</div>";
            }
            ?>

            <hr>

            <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>نظام التشغيل:</span>
                            <span><?php echo PHP_OS; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>إصدار PHP:</span>
                            <span><?php echo phpversion(); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>حد الذاكرة:</span>
                            <span><?php echo ini_get('memory_limit'); ?></span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>حد وقت التنفيذ:</span>
                            <span><?php echo ini_get('max_execution_time'); ?>s</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>حد رفع الملف:</span>
                            <span><?php echo ini_get('upload_max_filesize'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>حد POST:</span>
                            <span><?php echo ini_get('post_max_size'); ?></span>
                        </li>
                    </ul>
                </div>
            </div>

            <hr>

            <h3><i class="fas fa-rocket"></i> الحالة</h3>
            <?php if ($all_required_ok): ?>
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> النظام جاهز!</h4>
                    <p>جميع المتطلبات الأساسية متوفرة. يمكنك الآن استخدام مستخرج البيانات.</p>
                    <a href="index.php" class="btn btn-success btn-lg">
                        <i class="fas fa-play"></i> بدء الاستخدام
                    </a>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> يتطلب إصلاح!</h4>
                    <p>بعض المتطلبات الأساسية غير متوفرة. يرجى إصلاح المشاكل المذكورة أعلاه.</p>
                </div>
            <?php endif; ?>

            <hr>

            <h3><i class="fas fa-book"></i> تعليمات التثبيت على XAMPP</h3>
            <div class="accordion" id="installAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#step1">
                            الخطوة 1: نسخ الملفات
                        </button>
                    </h2>
                    <div id="step1" class="accordion-collapse collapse show" data-bs-parent="#installAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>انسخ مجلد <code>web_interface</code> إلى <code>C:\xampp\htdocs\</code></li>
                                <li>تأكد من وجود الملفات:
                                    <ul>
                                        <li><code>index.php</code> - الواجهة الرئيسية</li>
                                        <li><code>process.php</code> - معالج الملفات</li>
                                        <li><code>setup.php</code> - هذه الصفحة</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step2">
                            الخطوة 2: تشغيل XAMPP
                        </button>
                    </h2>
                    <div id="step2" class="accordion-collapse collapse" data-bs-parent="#installAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>شغل XAMPP Control Panel</li>
                                <li>ابدأ خدمة Apache</li>
                                <li>تأكد من أن Apache يعمل على المنفذ 80</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step3">
                            الخطوة 3: الوصول للبرنامج
                        </button>
                    </h2>
                    <div id="step3" class="accordion-collapse collapse" data-bs-parent="#installAccordion">
                        <div class="accordion-body">
                            <ol>
                                <li>افتح المتصفح</li>
                                <li>اذهب إلى: <code>http://localhost/web_interface/</code></li>
                                <li>أو: <code>http://127.0.0.1/web_interface/</code></li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step4">
                            الخطوة 4: تحسين الأداء (اختياري)
                        </button>
                    </h2>
                    <div id="step4" class="accordion-collapse collapse" data-bs-parent="#installAccordion">
                        <div class="accordion-body">
                            <p>لتحسين الأداء وزيادة دقة الاستخراج:</p>
                            <ol>
                                <li>ثبت Python على النظام</li>
                                <li>ثبت المكتبات المطلوبة:
                                    <pre><code>pip install PyPDF2 pdfplumber pandas</code></pre>
                                </li>
                                <li>ضع ملف <code>simple_pdf_extractor.py</code> في المجلد الرئيسي</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
