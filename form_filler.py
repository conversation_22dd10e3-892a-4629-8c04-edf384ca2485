#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Automated Form Filler for Solar Energy Application
ملء تلقائي لفورم طلب الطاقة الشمسية
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET
import re
from datetime import datetime

class AutoFormFiller:
    def __init__(self):
        self.pdf_data = {}
        self.word_structure = {}
        self.mapping_rules = {}
        
    def extract_pdf_data_simple(self, pdf_path):
        """استخراج بيانات بسيط من PDF"""
        try:
            with open(pdf_path, 'rb') as file:
                content = file.read()
            
            # تحويل إلى نص (محدود)
            text = content.decode('latin-1', errors='ignore')
            
            # استخراج معلومات أساسية
            extracted = {
                'company_name': 'FuYue Furniture',  # من اسم الملف
                'bank': '花旗银行',  # من اسم الملف
                'date': '2024.8.25',  # من اسم الملف
                'invoice_type': 'INVOICE',
                'file_size': len(content)
            }
            
            # البحث عن أرقام (مبالغ محتملة)
            numbers = re.findall(r'\d{1,3}(?:,\d{3})*(?:\.\d{2})?', text)
            if numbers:
                extracted['amounts'] = numbers[:5]  # أول 5 أرقام
            
            # البحث عن تواريخ إضافية
            dates = re.findall(r'2024[.-/]\d{1,2}[.-/]\d{1,2}', text)
            if dates:
                extracted['additional_dates'] = dates
            
            return extracted
            
        except Exception as e:
            print(f"خطأ في استخراج بيانات PDF: {e}")
            return {}
    
    def analyze_word_form(self, docx_path):
        """تحليل فورم الوورد"""
        try:
            with zipfile.ZipFile(docx_path, 'r') as docx_zip:
                content = docx_zip.read('word/document.xml')
                root = ET.fromstring(content)
                
                # استخراج النص
                text_elements = []
                for elem in root.iter():
                    if elem.text and elem.text.strip():
                        text_elements.append(elem.text.strip())
                
                full_text = ' '.join(text_elements)
                
                # تحليل الحقول
                fields = self.identify_form_fields(full_text)
                
                return {
                    'success': True,
                    'full_text': full_text,
                    'fields': fields,
                    'text_length': len(full_text)
                }
                
        except Exception as e:
            print(f"خطأ في تحليل فورم الوورد: {e}")
            return {'success': False, 'error': str(e)}
    
    def identify_form_fields(self, text):
        """تحديد حقول النموذج"""
        field_patterns = {
            'applicant_name': [
                'اسم مقدم الطلب', 'اسم الطالب', 'الاسم', 'اسم العميل'
            ],
            'company_name': [
                'اسم الشركة', 'الشركة', 'المؤسسة', 'اسم المؤسسة'
            ],
            'project_location': [
                'موقع المشروع', 'الموقع', 'العنوان', 'موقع التركيب'
            ],
            'system_capacity': [
                'قدرة النظام', 'السعة', 'القدرة المطلوبة', 'قدرة الطاقة'
            ],
            'project_type': [
                'نوع المشروع', 'نوع النظام', 'النوع'
            ],
            'contact_phone': [
                'رقم الهاتف', 'الهاتف', 'هاتف', 'رقم التواصل'
            ],
            'email': [
                'البريد الإلكتروني', 'الإيميل', 'البريد'
            ],
            'application_date': [
                'تاريخ الطلب', 'التاريخ', 'تاريخ التقديم'
            ],
            'estimated_cost': [
                'التكلفة المقدرة', 'المبلغ', 'التكلفة', 'القيمة'
            ]
        }
        
        identified_fields = {}
        
        for field_name, keywords in field_patterns.items():
            for keyword in keywords:
                if keyword in text:
                    # العثور على موقع الكلمة المفتاحية
                    start_pos = text.find(keyword)
                    if start_pos != -1:
                        # أخذ السياق المحيط
                        context_start = max(0, start_pos - 20)
                        context_end = min(len(text), start_pos + 100)
                        context = text[context_start:context_end]
                        
                        identified_fields[field_name] = {
                            'keyword': keyword,
                            'position': start_pos,
                            'context': context
                        }
                        break
        
        return identified_fields
    
    def create_mapping_rules(self, pdf_data, word_fields):
        """إنشاء قواعد ربط البيانات"""
        mapping = {}
        
        # ربط البيانات المستخرجة من PDF بحقول الوورد
        if 'company_name' in pdf_data and 'company_name' in word_fields:
            mapping['company_name'] = {
                'source': pdf_data['company_name'],
                'target_field': 'company_name',
                'confidence': 0.9
            }
        
        if 'date' in pdf_data and 'application_date' in word_fields:
            mapping['application_date'] = {
                'source': pdf_data['date'],
                'target_field': 'application_date',
                'confidence': 0.8
            }
        
        # إضافة بيانات افتراضية للحقول المفقودة
        default_values = {
            'applicant_name': 'يرجى إدخال الاسم',
            'project_type': 'نظام طاقة شمسية',
            'system_capacity': 'حسب الحاجة',
            'contact_phone': 'يرجى إدخال رقم الهاتف',
            'email': 'يرجى إدخال البريد الإلكتروني'
        }
        
        for field, default_value in default_values.items():
            if field in word_fields and field not in mapping:
                mapping[field] = {
                    'source': default_value,
                    'target_field': field,
                    'confidence': 0.3,
                    'is_default': True
                }
        
        return mapping
    
    def generate_filled_form_data(self, mapping_rules):
        """إنشاء بيانات النموذج المملوء"""
        filled_data = {}
        
        for field_name, rule in mapping_rules.items():
            filled_data[field_name] = {
                'value': rule['source'],
                'confidence': rule['confidence'],
                'is_default': rule.get('is_default', False)
            }
        
        return filled_data
    
    def process_files(self, pdf_path, word_path):
        """معالجة الملفات الرئيسية"""
        print("بدء معالجة الملفات...")
        
        # استخراج بيانات PDF
        print("1. استخراج بيانات من PDF...")
        self.pdf_data = self.extract_pdf_data_simple(pdf_path)
        print(f"   تم استخراج {len(self.pdf_data)} عنصر من PDF")
        
        # تحليل فورم الوورد
        print("2. تحليل فورم الوورد...")
        word_analysis = self.analyze_word_form(word_path)
        
        if not word_analysis['success']:
            print(f"   خطأ في تحليل الوورد: {word_analysis['error']}")
            return None
        
        self.word_structure = word_analysis
        print(f"   تم تحديد {len(word_analysis['fields'])} حقل في النموذج")
        
        # إنشاء قواعد الربط
        print("3. إنشاء قواعد ربط البيانات...")
        self.mapping_rules = self.create_mapping_rules(
            self.pdf_data, 
            word_analysis['fields']
        )
        print(f"   تم إنشاء {len(self.mapping_rules)} قاعدة ربط")
        
        # إنشاء البيانات المملوءة
        print("4. إنشاء البيانات المملوءة...")
        filled_data = self.generate_filled_form_data(self.mapping_rules)
        
        return {
            'pdf_data': self.pdf_data,
            'word_structure': self.word_structure,
            'mapping_rules': self.mapping_rules,
            'filled_data': filled_data
        }
    
    def save_results(self, results, output_file='form_filling_results.json'):
        """حفظ النتائج"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"تم حفظ النتائج في: {output_file}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ النتائج: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("نظام الملء التلقائي لفورم طلب الطاقة الشمسية")
    print("=" * 60)
    
    # مسارات الملفات
    pdf_file = "1_FuYue Furniture 花旗银行2024.8.25 INVOICE_已签章 (1).pdf"
    word_file = "طلب طاقة شمسية.docx"
    
    # التحقق من وجود الملفات
    if not os.path.exists(pdf_file):
        print(f"خطأ: ملف PDF غير موجود - {pdf_file}")
        return
    
    if not os.path.exists(word_file):
        print(f"خطأ: ملف الوورد غير موجود - {word_file}")
        return
    
    # إنشاء معالج النماذج
    form_filler = AutoFormFiller()
    
    try:
        # معالجة الملفات
        results = form_filler.process_files(pdf_file, word_file)
        
        if results:
            # عرض النتائج
            print("\n" + "=" * 40)
            print("ملخص النتائج:")
            print("=" * 40)
            
            print(f"البيانات المستخرجة من PDF:")
            for key, value in results['pdf_data'].items():
                print(f"  • {key}: {value}")
            
            print(f"\nالحقول المكتشفة في النموذج:")
            for field_name, field_info in results['word_structure']['fields'].items():
                print(f"  • {field_name}: {field_info['keyword']}")
            
            print(f"\nالبيانات المملوءة:")
            for field_name, field_data in results['filled_data'].items():
                status = "افتراضي" if field_data.get('is_default') else "مستخرج"
                print(f"  • {field_name}: {field_data['value']} ({status})")
            
            # حفظ النتائج
            form_filler.save_results(results)
            
            print("\n" + "=" * 40)
            print("تم إكمال العملية بنجاح!")
            print("=" * 40)
            
        else:
            print("فشل في معالجة الملفات")
            
    except Exception as e:
        print(f"خطأ في التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
