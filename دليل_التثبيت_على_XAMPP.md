# دليل تثبيت مستخرج البيانات من PDF على XAMPP
# Installation Guide for PDF Data Extractor on XAMPP

## 🎯 نظرة عامة / Overview

هذا الدليل يوضح كيفية تثبيت وتشغيل مستخرج البيانات من ملفات PDF على سيرفر XAMPP المحلي مع واجهة ويب سهلة الاستخدام.

---

## 📋 المتطلبات / Requirements

### المتطلبات الأساسية:
- ✅ **XAMPP** (Apache + PHP 7.4 أو أحدث)
- ✅ **متصفح ويب** حديث
- ✅ **ملفات PDF** للاختبار

### المتطلبات الاختيارية (لتحسين الأداء):
- 🔧 **Python 3.7+**
- 🔧 **مكتبات Python**: PyPDF2, pdfplumber, pandas

---

## 🚀 خطوات التثبيت / Installation Steps

### الخطوة 1: تحضير XAMPP
```bash
# 1. حمل وثبت XAMPP من:
https://www.apachefriends.org/download.html

# 2. شغل XAMPP Control Panel
# 3. ابدأ خدمة Apache
```

### الخطوة 2: نسخ ملفات البرنامج
```bash
# انسخ مجلد web_interface إلى:
C:\xampp\htdocs\pdf_extractor\

# أو إذا كنت تستخدم Linux/Mac:
/opt/lampp/htdocs/pdf_extractor/
```

### الخطوة 3: إعداد الصلاحيات
```bash
# تأكد من أن المجلدات التالية قابلة للكتابة:
- uploads/
- output/

# في Windows: كليك يمين > Properties > Security > Edit
# في Linux/Mac: chmod 755 uploads/ output/
```

### الخطوة 4: اختبار التثبيت
```bash
# افتح المتصفح واذهب إلى:
http://localhost/pdf_extractor/setup.php

# أو:
http://127.0.0.1/pdf_extractor/setup.php
```

---

## 🖥️ الواجهات المتاحة / Available Interfaces

### 1. صفحة الإعداد والفحص:
**الرابط:** `http://localhost/pdf_extractor/setup.php`
- فحص المتطلبات
- معلومات النظام
- تعليمات التثبيت

### 2. الواجهة الرئيسية:
**الرابط:** `http://localhost/pdf_extractor/index.php`
- رفع الملفات
- معالجة البيانات
- تحميل النتائج

---

## 📁 هيكل الملفات / File Structure

```
pdf_extractor/
├── index.php          # الواجهة الرئيسية
├── process.php        # معالج الملفات الخلفي
├── setup.php          # صفحة الإعداد والفحص
├── uploads/           # مجلد الملفات المرفوعة (مؤقت)
├── output/            # مجلد النتائج
└── README.md          # هذا الملف
```

---

## 🔧 الإعدادات المتقدمة / Advanced Configuration

### تحسين أداء PHP:
```ini
# في ملف php.ini:
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 512M
```

### إضافة دعم Python (اختياري):
```bash
# 1. ثبت Python
# 2. ثبت المكتبات:
pip install PyPDF2 pdfplumber pandas openpyxl

# 3. ضع ملف simple_pdf_extractor.py في المجلد الرئيسي
```

---

## 🎮 كيفية الاستخدام / How to Use

### الطريقة البسيطة:
1. **افتح الواجهة:** `http://localhost/pdf_extractor/`
2. **ارفع الملفات:** اسحب وأفلت ملفات PDF أو اضغط "اختيار ملفات"
3. **ابدأ المعالجة:** اضغط "بدء استخراج البيانات"
4. **حمل النتائج:** اضغط "تحميل ملف Excel" عند الانتهاء

### الميزات المتاحة:
- ✅ **رفع متعدد:** رفع عدة ملفات PDF مرة واحدة
- ✅ **سحب وإفلات:** سحب الملفات مباشرة للواجهة
- ✅ **معاينة الملفات:** عرض قائمة الملفات المختارة
- ✅ **شريط التقدم:** متابعة تقدم المعالجة
- ✅ **تحميل فوري:** تحميل النتائج مباشرة

---

## 📊 البيانات المستخرجة / Extracted Data

البرنامج يستخرج البيانات التالية من ملفات PDF:

### البيانات الأساسية:
- 🏢 **اسم الشركة المستفيدة**
- 📍 **العنوان**
- 📞 **رقم الهاتف**
- 📧 **البريد الإلكتروني**

### البيانات المصرفية:
- 🏦 **اسم البنك المستفيد**
- 💳 **رقم الحساب**
- 🔢 **رمز السويفت**
- 🏛️ **عنوان البنك**
- 💰 **المبلغ**

---

## 🔍 طرق الاستخراج / Extraction Methods

### 1. الاستخراج الأساسي (PHP):
- استخراج من أسماء الملفات
- بحث أنماط بسيطة في النص
- سرعة عالية، دقة متوسطة

### 2. الاستخراج المتقدم (Python):
- استخراج نص متقدم من PDF
- دعم OCR للصور
- دقة عالية، سرعة متوسطة

---

## 🛠️ حل المشاكل الشائعة / Troubleshooting

### مشكلة: "Apache لا يبدأ"
**الحل:**
```bash
# تحقق من المنافذ المستخدمة
netstat -an | find "80"

# غير المنفذ في httpd.conf إذا لزم الأمر
Listen 8080
```

### مشكلة: "Permission denied"
**الحل:**
```bash
# Windows: تشغيل XAMPP كمدير
# Linux/Mac: تغيير صلاحيات المجلدات
chmod 755 uploads/ output/
```

### مشكلة: "File too large"
**الحل:**
```ini
# في php.ini:
upload_max_filesize = 50M
post_max_size = 50M
```

### مشكلة: "Python not found"
**الحل:**
- البرنامج سيعمل بدون Python (استخراج أساسي)
- لتحسين الأداء، ثبت Python والمكتبات المطلوبة

---

## 🔒 الأمان / Security

### احتياطات الأمان:
- ✅ **تنظيف الملفات:** حذف الملفات المؤقتة تلقائياً
- ✅ **فلترة الملفات:** قبول ملفات PDF فقط
- ✅ **أسماء آمنة:** تنظيف أسماء الملفات
- ✅ **حدود الحجم:** تحديد حجم الرفع الأقصى

### للاستخدام الإنتاجي:
- 🔐 إضافة مصادقة المستخدمين
- 🔐 تشفير الاتصالات (HTTPS)
- 🔐 تحديد صلاحيات الوصول
- 🔐 مراقبة السجلات

---

## 📈 الأداء المتوقع / Expected Performance

### معدلات المعالجة:
- **ملف صغير (< 1MB):** 1-3 ثواني
- **ملف متوسط (1-5MB):** 3-10 ثواني
- **ملف كبير (> 5MB):** 10-30 ثانية

### دقة الاستخراج:
- **الاستخراج الأساسي:** 60-80%
- **مع Python:** 80-95%
- **مع OCR:** 70-90% (للصور)

---

## 🔄 التحديثات والصيانة / Updates & Maintenance

### تحديث البرنامج:
1. احتفظ بنسخة احتياطية من المجلد
2. استبدل الملفات الجديدة
3. اختبر الوظائف الأساسية

### الصيانة الدورية:
- تنظيف مجلد uploads/
- تنظيف مجلد output/
- مراجعة سجلات الأخطاء
- تحديث المكتبات

---

## 📞 الدعم / Support

### للمساعدة:
1. **تحقق من صفحة الإعداد:** `setup.php`
2. **راجع سجلات Apache:** `xampp/apache/logs/error.log`
3. **اختبر ملف واحد أولاً**

### الأخطاء الشائعة:
- **500 Internal Server Error:** تحقق من صلاحيات الملفات
- **404 Not Found:** تأكد من مسار الملفات
- **Upload failed:** تحقق من إعدادات PHP

---

## ✅ قائمة التحقق السريع / Quick Checklist

قبل البدء، تأكد من:
- [ ] XAMPP مثبت ويعمل
- [ ] Apache يعمل على المنفذ 80
- [ ] ملفات البرنامج في htdocs/
- [ ] مجلدات uploads/ و output/ قابلة للكتابة
- [ ] صفحة setup.php تعرض "النظام جاهز"
- [ ] يمكن الوصول للواجهة الرئيسية

---

## 🎉 الخلاصة / Conclusion

الآن لديك نظام شامل لاستخراج البيانات من ملفات PDF يعمل على سيرفر XAMPP المحلي مع:

### ✅ المميزات:
- واجهة ويب سهلة الاستخدام
- رفع متعدد للملفات
- استخراج تلقائي للبيانات
- تصدير إلى Excel
- يعمل بدون إنترنت

### 🚀 للبدء:
1. شغل XAMPP
2. اذهب إلى `http://localhost/pdf_extractor/`
3. ارفع ملفات PDF
4. احصل على النتائج!

---

**🎊 مبروك! البرنامج جاهز للاستخدام على سيرفرك المحلي!**
