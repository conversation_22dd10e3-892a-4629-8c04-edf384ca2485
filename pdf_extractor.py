#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Data Extractor for Solar Energy Application Form
استخراج البيانات من ملف PDF لملء فورم طلب الطاقة الشمسية
"""

import PyPDF2
import pdfplumber
import re
from typing import Dict, Any
import json

class PDFDataExtractor:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.extracted_data = {}
    
    def extract_with_pypdf2(self) -> str:
        """استخراج النص باستخدام PyPDF2"""
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"خطأ في استخراج النص باستخدام PyPDF2: {e}")
            return ""
    
    def extract_with_pdfplumber(self) -> str:
        """استخراج النص باستخدام pdfplumber (أكثر دقة)"""
        try:
            text = ""
            with pdfplumber.open(self.pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            print(f"خطأ في استخراج النص باستخدام pdfplumber: {e}")
            return ""
    
    def extract_invoice_data(self, text: str) -> Dict[str, Any]:
        """استخراج بيانات الفاتورة من النص"""
        data = {}
        
        # البحث عن التاريخ
        date_patterns = [
            r'(\d{4}[-./]\d{1,2}[-./]\d{1,2})',
            r'(\d{1,2}[-./]\d{1,2}[-./]\d{4})',
            r'Date[:\s]*([^\n]+)',
            r'تاريخ[:\s]*([^\n]+)'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['date'] = match.group(1).strip()
                break
        
        # البحث عن اسم الشركة أو العميل
        company_patterns = [
            r'Company[:\s]*([^\n]+)',
            r'Client[:\s]*([^\n]+)',
            r'Customer[:\s]*([^\n]+)',
            r'شركة[:\s]*([^\n]+)',
            r'عميل[:\s]*([^\n]+)',
            r'FuYue Furniture'
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['company_name'] = match.group(1).strip() if match.groups() else match.group(0)
                break
        
        # البحث عن المبلغ
        amount_patterns = [
            r'Total[:\s]*([0-9,]+\.?\d*)',
            r'Amount[:\s]*([0-9,]+\.?\d*)',
            r'المبلغ[:\s]*([0-9,]+\.?\d*)',
            r'الإجمالي[:\s]*([0-9,]+\.?\d*)',
            r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
        ]
        
        for pattern in amount_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                data['amount'] = matches[-1]  # أخذ آخر مبلغ موجود
                break
        
        # البحث عن رقم الفاتورة
        invoice_patterns = [
            r'Invoice[:\s#]*([A-Z0-9-]+)',
            r'رقم الفاتورة[:\s]*([A-Z0-9-]+)',
            r'INV[:\s#]*([A-Z0-9-]+)'
        ]
        
        for pattern in invoice_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['invoice_number'] = match.group(1).strip()
                break
        
        # البحث عن العنوان
        address_patterns = [
            r'Address[:\s]*([^\n]+)',
            r'العنوان[:\s]*([^\n]+)'
        ]
        
        for pattern in address_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['address'] = match.group(1).strip()
                break
        
        return data
    
    def extract_all_data(self) -> Dict[str, Any]:
        """استخراج جميع البيانات من ملف PDF"""
        print(f"جاري استخراج البيانات من: {self.pdf_path}")
        
        # محاولة الاستخراج بطريقتين مختلفتين
        text_pypdf2 = self.extract_with_pypdf2()
        text_pdfplumber = self.extract_with_pdfplumber()
        
        # استخدام النص الأطول (الأكثر اكتمالاً)
        text = text_pdfplumber if len(text_pdfplumber) > len(text_pypdf2) else text_pypdf2
        
        if not text.strip():
            print("تعذر استخراج النص من ملف PDF")
            return {}
        
        print("النص المستخرج:")
        print("-" * 50)
        print(text[:500] + "..." if len(text) > 500 else text)
        print("-" * 50)
        
        # استخراج البيانات المهيكلة
        extracted_data = self.extract_invoice_data(text)
        
        # إضافة النص الكامل للمرجع
        extracted_data['full_text'] = text
        
        return extracted_data

def main():
    """الدالة الرئيسية لاختبار استخراج البيانات"""
    pdf_file = "1_FuYue Furniture 花旗银行2024.8.25 INVOICE_已签章 (1).pdf"
    
    extractor = PDFDataExtractor(pdf_file)
    data = extractor.extract_all_data()
    
    print("\nالبيانات المستخرجة:")
    print("=" * 50)
    for key, value in data.items():
        if key != 'full_text':  # تجنب طباعة النص الكامل
            print(f"{key}: {value}")
    
    # حفظ البيانات في ملف JSON
    with open('extracted_data.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"\nتم حفظ البيانات في: extracted_data.json")

if __name__ == "__main__":
    main()
